"""
File Processing Service for Multimodal AI Input
Handles images, documents, audio files for SynTour AI
"""

import os
import io
import base64
from typing import Optional, Dict, List, Tuple
from PIL import Image
import magic
# import speech_recognition as sr  # Not included in requirements
# from pydub import AudioSegment  # Module has compatibility issues with Python 3.13
import logging

logger = logging.getLogger(__name__)

class FileProcessor:
    
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    SUPPORTED_AUDIO_FORMATS = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}
    SUPPORTED_DOC_FORMATS = {'.txt', '.pdf', '.doc', '.docx'}
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_IMAGE_SIZE = (1920, 1080)
    
    @staticmethod
    async def process_file(file_content: bytes, filename: str) -> Dict:
        """Process uploaded file and extract relevant information"""
        try:
            file_ext = os.path.splitext(filename)[1].lower()
            file_size = len(file_content)
            
            if file_size > FileProcessor.MAX_FILE_SIZE:
                raise ValueError(f"File too large: {file_size} bytes (max: {FileProcessor.MAX_FILE_SIZE})")
            
            # Detect file type
            mime_type = magic.from_buffer(file_content, mime=True)
            
            result = {
                "filename": filename,
                "file_type": file_ext,
                "mime_type": mime_type,
                "file_size": file_size,
                "processed": False,
                "content": None,
                "description": None,
                "error": None
            }
            
            # Process based on file type
            if file_ext in FileProcessor.SUPPORTED_IMAGE_FORMATS:
                result.update(await FileProcessor._process_image(file_content, filename))
            elif file_ext in FileProcessor.SUPPORTED_AUDIO_FORMATS:
                result.update(await FileProcessor._process_audio(file_content, filename))
            elif file_ext in FileProcessor.SUPPORTED_DOC_FORMATS:
                result.update(await FileProcessor._process_document(file_content, filename))
            else:
                result["error"] = f"Unsupported file type: {file_ext}"
            
            return result
            
        except Exception as e:
            logger.error(f"File processing error: {str(e)}")
            return {
                "filename": filename,
                "processed": False,
                "error": str(e)
            }
    
    @staticmethod
    async def _process_image(file_content: bytes, filename: str) -> Dict:
        """Process image files for AI analysis"""
        try:
            # Open image
            image = Image.open(io.BytesIO(file_content))
            
            # Get image info
            width, height = image.size
            format_type = image.format
            mode = image.mode
            
            # Resize if too large
            if width > FileProcessor.MAX_IMAGE_SIZE[0] or height > FileProcessor.MAX_IMAGE_SIZE[1]:
                image.thumbnail(FileProcessor.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                logger.info(f"Resized image {filename} from {width}x{height} to {image.size}")
            
            # Convert to RGB if necessary
            if mode not in ('RGB', 'L'):
                image = image.convert('RGB')
            
            # Convert to base64 for API transmission
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            base64_image = base64.b64encode(buffer.getvalue()).decode()
            
            # Generate description for AI
            description = f"Image: {filename}, Size: {image.size[0]}x{image.size[1]}, Format: {format_type}"
            
            return {
                "processed": True,
                "content": base64_image,
                "description": description,
                "metadata": {
                    "width": image.size[0],
                    "height": image.size[1],
                    "format": format_type,
                    "mode": mode
                }
            }
            
        except Exception as e:
            return {"error": f"Image processing failed: {str(e)}"}
    
    @staticmethod
    async def _process_audio(file_content: bytes, filename: str) -> Dict:
        """Process audio files (simplified - audio processing disabled due to module compatibility)"""
        try:
            # Audio processing disabled due to pydub compatibility issues with Python 3.13
            text = "[Audio processing disabled - pydub compatibility issues with Python 3.13]"
            file_size = len(file_content)
            
            description = f"Audio: {filename}, Size: {file_size/1024:.1f}KB, Status: {text}"
            
            return {
                "processed": True,
                "content": text,
                "description": description,
                "metadata": {
                    "file_size_bytes": file_size,
                    "status": "audio_processing_disabled"
                }
            }
            
        except Exception as e:
            return {"error": f"Audio processing failed: {str(e)}"}
    
    @staticmethod
    async def _process_document(file_content: bytes, filename: str) -> Dict:
        """Process document files"""
        try:
            file_ext = os.path.splitext(filename)[1].lower()
            
            if file_ext == '.txt':
                # Simple text file
                try:
                    text = file_content.decode('utf-8')
                except UnicodeDecodeError:
                    text = file_content.decode('latin1')
                
                # Limit text length
                if len(text) > 5000:
                    text = text[:5000] + "... [truncated]"
                
                description = f"Text document: {filename}, Length: {len(text)} characters"
                
                return {
                    "processed": True,
                    "content": text,
                    "description": description,
                    "metadata": {
                        "length": len(text),
                        "type": "plain_text"
                    }
                }
            
            else:
                # For PDF, DOC, DOCX - placeholder implementation
                return {
                    "processed": False,
                    "error": f"Document type {file_ext} processing not yet implemented. Please convert to .txt format."
                }
                
        except Exception as e:
            return {"error": f"Document processing failed: {str(e)}"}
    
    @staticmethod
    def get_file_info(filename: str) -> Dict:
        """Get file type information"""
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext in FileProcessor.SUPPORTED_IMAGE_FORMATS:
            return {"type": "image", "category": "visual", "supported": True}
        elif file_ext in FileProcessor.SUPPORTED_AUDIO_FORMATS:
            return {"type": "audio", "category": "speech", "supported": True}
        elif file_ext in FileProcessor.SUPPORTED_DOC_FORMATS:
            return {"type": "document", "category": "text", "supported": True}
        else:
            return {"type": "unknown", "category": "unsupported", "supported": False}
