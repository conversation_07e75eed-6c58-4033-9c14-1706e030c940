"use client";
import { useRef, useState, useEffect } from "react";
import { Plus, Send, FileUp, ImagePlus, X, FileText } from "lucide-react";
import { useToast, ToastContainer } from "./Toast";
import { AIService } from "../services/aiService";
import type { Message } from "./ChatInterface";
import VoiceWave from "./VoiceWave";
import VoiceRecorder from "./VoiceRecorder";

interface FileItem {
  id: string;
  file: File;
  type: 'image' | 'file';
  preview?: string;
}

interface ChatBarV2Props {
  messages: Message[];
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function ChatBarV2({ messages, setMessages, isLoading, setIsLoading }: ChatBarV2Props) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const [text, setText] = useState("");
  const [files, setFiles] = useState<FileItem[]>([]);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isReceivingVoice, setIsReceivingVoice] = useState(false);
  const [interimTranscript, setInterimTranscript] = useState("");
  const { toasts, showToast, removeToast } = useToast();

  // 自动调整textarea高度的函数
  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = 160; // 增加最大高度到160px，约6-7行
    const newHeight = Math.min(scrollHeight, maxHeight);
    textarea.style.height = newHeight + 'px';
  };

  // 监听text变化，自动调整textarea高度
  useEffect(() => {
    if (textareaRef.current) {
      adjustTextareaHeight(textareaRef.current);
    }
  }, [text]);

  // Listen for mytour.compose events from planner
  useEffect(() => {
    function onCompose(e: any) {
      try {
        const t = typeof e.detail === "string" ? e.detail : "";
        if (!t) return;
        setFiles([]); // 清空附件
        setText(t);      // 填入文本（不自动发送）
        setTimeout(() => window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" }), 50);
      } catch { }
    }
    window.addEventListener("mytour.compose", onCompose as EventListener);
    return () => window.removeEventListener("mytour.compose", onCompose as EventListener);
  }, []);

  async function onSend() {
    if ((!text.trim() && files.length === 0) || isLoading) return;

    const messageContent = text.trim();
    const userFiles = files.map(f => ({ name: f.file.name, type: f.type }));

    // Add user message
    const userMessage: Message = {
      id: Math.random().toString(36).substring(2, 11),
      type: 'user',
      content: messageContent || '(Files attached)',
      files: userFiles.length > 0 ? userFiles : undefined,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Clear inputs immediately
    const currentText = text;
    const currentFiles = [...files];
    setText("");
    setFiles([]);
    if (textareaRef.current) {
      textareaRef.current.value = "";
      textareaRef.current.style.height = '32px';
    }

    try {
      let response;

      if (currentFiles.length > 0) {
        // Use multimodal endpoint for files
        const fileArray = currentFiles.map(f => f.file);
        response = await AIService.multimodalChat(currentText, fileArray, "Travel planning assistance");
      } else {
        // Use regular chat endpoint
        response = await AIService.chat({
          message: currentText,
          context: "Travel planning assistance"
        });
      }

      // Add AI response
      const aiMessage: Message = {
        id: Math.random().toString(36).substring(2, 11),
        type: 'assistant',
        content: response.success ? response.response || "I received your message but couldn't generate a response." : "Sorry, I encountered an error.",
        timestamp: new Date(),
        error: response.success ? undefined : response.error,
        is_truncated: response.is_truncated || false,
        continuation_id: response.continuation_id
      };

      setMessages(prev => [...prev, aiMessage]);

      // Show success toast only for successful responses
      if (response.success) {
        showToast({
          title: "✅ Response Received",
          message: "SynTour has responded to your message!",
          variant: "success"
        });
      } else {
        showToast({
          title: "⚠️ Error",
          message: response.error || "Failed to get AI response",
          variant: "warning"
        });
      }

    } catch (error: any) {
      console.error('Chat error:', error);

      // Add error message
      const errorMessage: Message = {
        id: Math.random().toString(36).substring(2, 11),
        type: 'assistant',
        content: "Sorry, I encountered a technical error.",
        timestamp: new Date(),
        error: error.message || "Connection error"
      };

      setMessages(prev => [...prev, errorMessage]);

      showToast({
        title: "❌ Connection Error",
        message: "Failed to connect to SynTour AI service",
        variant: "warning"
      });
    } finally {
      setIsLoading(false);
    }
  }

  function handleAddFiles(newFiles: File[], type: 'image' | 'file') {
    // 检查总附件数量限制（图片+文件最多3个）
    const currentTotalCount = files.length;
    if (currentTotalCount + newFiles.length > 3) {
      showToast({
        title: "⚠️ Upload limit reached",
        message: "You can upload up to 3 files and images combined. Please remove some attachments before adding more.",
        variant: "warning"
      });
      return false; // 返回false表示添加失败
    }
    return true; // 返回true表示可以添加
  }

  function onFileChange(e: React.ChangeEvent<HTMLInputElement>, type: 'image' | 'file') {
    const selectedFiles = Array.from(e.target.files || []);

    // 检查是否可以添加文件
    if (!handleAddFiles(selectedFiles, type)) {
      return;
    }

    if (type === 'image') {
      // 处理图片文件
      selectedFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const newFile: FileItem = {
            id: Math.random().toString(36).substring(2, 11),
            file,
            type: 'image',
            preview: e.target?.result as string
          };
          setFiles(prev => [...prev, newFile]);
        };
        reader.readAsDataURL(file);
      });
    } else {
      // 处理普通文件
      selectedFiles.forEach(file => {
        const newFile: FileItem = {
          id: Math.random().toString(36).substring(2, 11),
          file,
          type: 'file'
        };
        setFiles(prev => [...prev, newFile]);
      });
    }

    setMenuOpen(false);
    // 清空input值，允许重复选择相同文件
    e.target.value = '';
  }

  function removeFile(id: string) {
    setFiles(prev => prev.filter(f => f.id !== id));
  }

  function openFileSelector(type: 'image' | 'file') {
    if (type === 'image') {
      imageInputRef.current?.click();
    } else {
      fileInputRef.current?.click();
    }
  }

  const inputRef = useRef<HTMLInputElement>(null);

  function send() {
    if (!text.trim() && files.length === 0) return;
    onSend();
  }

  return (
    <>
      <div className="fixed left-0 right-0 bottom-4 z-40">
        <div className="flex justify-center px-4">
          <div className="relative w-full max-w-4xl">
            {/* 文件预览区域 */}
            {files.length > 0 && (
              <div className="mb-3 p-3 bg-white/90 backdrop-blur border border-gray-200 rounded-xl shadow-soft">
                <div className="flex flex-wrap gap-2">
                  {files.map((fileItem) => (
                    <div key={fileItem.id} className="relative group">
                      {fileItem.type === 'image' ? (
                        <div className="relative">
                          <img
                            src={fileItem.preview}
                            alt={fileItem.file.name}
                            className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                          />
                          <button
                            onClick={() => removeFile(fileItem.id)}
                            className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ) : (
                        <div className="relative w-16 h-16 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                          <FileUp className="w-6 h-6 text-gray-500" />
                          <button
                            onClick={() => removeFile(fileItem.id)}
                            className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      )}
                      <div className="text-xs text-gray-600 mt-1 text-center max-w-16 truncate">
                        {fileItem.file.name.length > 12
                          ? fileItem.file.name.substring(0, 12) + '...'
                          : fileItem.file.name}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Chat Input Area */}
            <div className="bg-white rounded-full border border-gray-200 shadow-sm p-2 focus-within:ring-2 focus-within:ring-black/5 transition-all">
              <div className="flex items-center gap-2">
                {/* 附件菜单按钮 */}
                <div className="relative">
                  <button
                    onClick={() => setMenuOpen(!menuOpen)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    aria-label="Add attachment"
                  >
                    <Plus className="w-5 h-5" />
                  </button>

                  {/* 附件菜单 - 优化图标设计 */}
                  {menuOpen && (
                    <div className="absolute bottom-full left-0 mb-2 bg-transparent backdrop-blur-md rounded-xl shadow-lg border border-gray-200/30 p-2 min-w-[140px]">
                      <button
                        onClick={() => openFileSelector('image')}
                        disabled={files.length >= 3}
                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                      >
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white group-hover:scale-105 transition-transform">
                          <ImagePlus className="w-4 h-4" />
                        </div>
                        <span className="font-medium">Add Image</span>
                      </button>

                      <button
                        onClick={() => openFileSelector('file')}
                        disabled={files.length >= 3}
                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                      >
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center text-white group-hover:scale-105 transition-transform">
                          <FileText className="w-4 h-4" />
                        </div>
                        <span className="font-medium">Add File</span>
                      </button>
                    </div>
                  )}
                </div>

                {/* 文本输入框 */}
                <div className="flex-1 relative">
                  <input
                    ref={inputRef}
                    type="text"
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        send();
                      }
                    }}
                    placeholder={isReceivingVoice ? `Listening: "${interimTranscript}"` : "Ask anything to SynTour..."}
                    className="w-full px-4 py-2 bg-transparent outline-none text-[15px] sm:text-[16px] placeholder-gray-400"
                    disabled={isLoading}
                  />
                </div>

                {/* 右侧功能按钮组 */}
                <div className="flex items-center gap-2">
                  {/* 语音输入按钮 - 使用VoiceRecorder组件 */}
                  <VoiceRecorder
                    onTranscriptChange={(transcript, isFinal) => {
                      if (isFinal) {
                        setIsReceivingVoice(false);
                        setInterimTranscript("");
                        setText(prevText => {
                          return prevText.trim() ? `${prevText.trim()} ${transcript}` : transcript;
                        });
                      } else {
                        setIsReceivingVoice(true);
                        setInterimTranscript(transcript);
                      }
                    }}
                  />

                  {/* 语音对话按钮 */}
                  <button
                    onClick={() => {
                      // TODO: 实现语音对话功能
                      alert("语音对话功能即将推出！");
                    }}
                    className="w-9 h-9 rounded-full flex items-center justify-center bg-white border border-gray-200 shadow-sm hover:bg-gray-50 transition-all duration-200"
                    aria-label="Voice conversation"
                    title="语音对话"
                  >
                    <VoiceWave className="w-4 h-4 text-gray-600" />
                  </button>

                  {/* 发送按钮 */}
                  <button
                    onClick={send}
                    disabled={!text.trim() || isLoading}
                    className={`w-9 h-9 rounded-full flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${text.trim() && !isLoading
                      ? "bg-gray-100 hover:bg-gray-200 text-gray-700"
                      : "bg-gray-100 text-gray-400"
                      }`}
                    aria-label="Send message"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={(e) => onFileChange(e, 'file')}
      />
      <input
        ref={imageInputRef}
        type="file"
        multiple
        accept="image/*"
        className="hidden"
        onChange={(e) => onFileChange(e, 'image')}
      />

      {/* Toast通知 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </>
  );
}
