#!/usr/bin/env python3
"""
Comprehensive Google Places API Test Suite
Tests Place Details and Place Photo functionality using the new Places API
Documentation: https://developers.google.com/maps/documentation/places/web-service/place-details
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Import service functions
from app.services.googleplaces_service import (
    get_place_details,
    get_place_photo,
    get_place_summary,
    get_place_photos_info,
    validate_place_id,
    validate_photo_reference
)
from app.models.googleplaces_model import GooglePlacesError

class GooglePlacesTestSuite:
    def __init__(self):
        self.results = []
        self.test_place_ids = [
            "ChIJN1t_tDeuEmsRUsoyG83frY4",  # Google Sydney Office
            "ChIJrTLr-GyuEmsRBfy61i59si0",  # Sydney Opera House
            "ChIJqVs_VjdZdkgRXFUQkq6Z6Xs",  # Big Ben, London (corrected)
            "ChIJOwg_06VPwokRYv534QaPC8g",  # Empire State Building, NYC
        ]
    
    async def run_test(self, test_name: str, test_func):
        """Run a single test and record results"""
        try:
            print(f"🧪 Testing: {test_name}")
            result = await test_func()
            
            self.results.append({
                "test": test_name,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "details": result
            })
            print(f"✅ PASS - {test_name}")
            return True
            
        except Exception as e:
            error_details = {
                "error": str(e),
                "type": type(e).__name__
            }
            
            self.results.append({
                "test": test_name,
                "success": False,
                "timestamp": datetime.now().isoformat(),
                "details": error_details
            })
            print(f"❌ FAIL - {test_name}")
            print(f"   Error: {error_details}")
            return False
    
    async def test_environment_setup(self):
        """Test that environment variables are properly configured"""
        api_key = os.getenv("GOOGLE_PLACES_API_KEY")
        
        if not api_key:
            raise ValueError("GOOGLE_PLACES_API_KEY not found in environment")
        
        return {
            "api_key_configured": True,
            "api_key_preview": f"{api_key[:10]}..." if len(api_key) > 10 else "short_key"
        }
    
    async def test_place_details_basic(self):
        """Test basic place details retrieval"""
        place_id = self.test_place_ids[0]  # Google Sydney Office
        
        response = await get_place_details(place_id)
        
        # Validate response structure
        assert response.result.place_id == place_id
        assert response.result.name is not None
        assert response.result.formatted_address is not None
        assert response.result.geometry is not None
        assert response.result.geometry.location is not None
        
        return {
            "place_id": response.result.place_id,
            "name": response.result.name,
            "address": response.result.formatted_address,
            "coordinates": response.result.geometry.location,
            "types": response.result.types[:3] if response.result.types else []
        }
    
    async def test_place_details_with_fields(self):
        """Test place details with specific fields"""
        place_id = self.test_place_ids[1]  # Sydney Opera House
        fields = ["place_id", "name", "rating", "user_ratings_total", "photos"]
        
        response = await get_place_details(place_id, fields=fields)
        
        # Validate that only requested fields are populated (where available)
        assert response.result.place_id == place_id
        assert response.result.name is not None
        
        return {
            "place_id": response.result.place_id,
            "name": response.result.name,
            "rating": response.result.rating,
            "total_ratings": response.result.user_ratings_total,
            "has_photos": bool(response.result.photos),
            "photos_count": len(response.result.photos) if response.result.photos else 0
        }
    
    async def test_place_details_with_language(self):
        """Test place details with different language"""
        place_id = self.test_place_ids[3]  # Empire State Building, NYC
        
        # Test with French language
        response = await get_place_details(place_id, language="fr")
        
        return {
            "place_id": response.result.place_id,
            "name": response.result.name,
            "address": response.result.formatted_address,
            "language_requested": "fr"
        }
    
    async def test_place_photos_info(self):
        """Test getting place photos information"""
        place_id = self.test_place_ids[1]  # Sydney Opera House (likely to have photos)
        
        photos_info = await get_place_photos_info(place_id)
        
        if not photos_info:
            # Try another place if no photos
            place_id = self.test_place_ids[3]  # Empire State Building
            photos_info = await get_place_photos_info(place_id)
        
        return {
            "place_id": place_id,
            "photos_count": len(photos_info),
            "first_photo": photos_info[0] if photos_info else None,
            "photo_dimensions": [(p["width"], p["height"]) for p in photos_info[:3]]
        }
    
    async def test_place_photo_download(self):
        """Test downloading a place photo"""
        # First get a place with photos
        place_id = self.test_place_ids[1]  # Sydney Opera House
        photos_info = await get_place_photos_info(place_id)
        
        if not photos_info:
            # Try another place
            place_id = self.test_place_ids[3]  # Empire State Building
            photos_info = await get_place_photos_info(place_id)
        
        if not photos_info:
            raise ValueError("No photos available for test places")
        
        # Download the first photo
        photo_ref = photos_info[0]["photo_reference"]
        photo_data = await get_place_photo(photo_ref, max_width=400)
        
        return {
            "photo_reference": photo_ref[:20] + "...",
            "photo_size_bytes": len(photo_data),
            "photo_size_kb": round(len(photo_data) / 1024, 2),
            "is_valid_image": photo_data.startswith(b'\xff\xd8\xff')  # JPEG header
        }
    
    async def test_place_summary(self):
        """Test place summary functionality"""
        place_id = self.test_place_ids[0]  # Google Sydney Office
        
        summary = await get_place_summary(place_id)
        
        return {
            "place_id": summary["place_id"],
            "name": summary["name"],
            "has_coordinates": bool(summary["coordinates"]["lat"] and summary["coordinates"]["lng"]),
            "types_count": len(summary["types"]),
            "has_rating": summary["rating"] is not None,
            "business_status": summary["business_status"]
        }
    
    async def test_validation_functions(self):
        """Test utility validation functions"""
        # Test place_id validation
        valid_place_ids = [
            "ChIJN1t_tDeuEmsRUsoyG83frY4",
            "ChIJ2WrMN9MDdkgRo4_MUy3kSIM"
        ]
        
        invalid_place_ids = [
            "",
            "short",
            None,
            "invalid-place-id-format"
        ]
        
        # Test photo reference validation
        valid_photo_refs = [
            "ATplDJa5_jMiI7X7rl5xH2o-4Y8Ahr6gcVXpe_fWrAi0ZV2_YgxkuLDW8FizrPfUiOdSlJez",
            "ATplDJYS0PDBSkJj_zL4hl_cO8QJMmz4CBADF8MmJ-E_VFbVTxPNegXFag"
        ]
        
        invalid_photo_refs = [
            "",
            "short",
            None,
            "invalid"
        ]
        
        return {
            "valid_place_ids_passed": all(validate_place_id(pid) for pid in valid_place_ids),
            "invalid_place_ids_failed": all(not validate_place_id(pid) for pid in invalid_place_ids),
            "valid_photo_refs_passed": all(validate_photo_reference(ref) for ref in valid_photo_refs),
            "invalid_photo_refs_failed": all(not validate_photo_reference(ref) for ref in invalid_photo_refs)
        }
    
    async def test_error_handling(self):
        """Test error handling for invalid requests"""
        error_tests = []
        
        # Test invalid place_id
        try:
            await get_place_details("invalid_place_id")
            error_tests.append({"test": "invalid_place_id", "raised_error": False})
        except (GooglePlacesError, ValueError):
            error_tests.append({"test": "invalid_place_id", "raised_error": True})
        
        # Test invalid photo reference
        try:
            await get_place_photo("invalid_photo_ref")
            error_tests.append({"test": "invalid_photo_ref", "raised_error": False})
        except (GooglePlacesError, ValueError):
            error_tests.append({"test": "invalid_photo_ref", "raised_error": True})
        
        # Test invalid fields
        try:
            await get_place_details(self.test_place_ids[0], fields=["invalid_field"])
            error_tests.append({"test": "invalid_fields", "raised_error": False})
        except (GooglePlacesError, ValueError):
            error_tests.append({"test": "invalid_fields", "raised_error": True})
        
        return {
            "error_tests": error_tests,
            "all_errors_handled": all(test["raised_error"] for test in error_tests)
        }
    
    async def run_all_tests(self):
        """Run all tests in the suite"""
        print("🗺️ Google Places API Test Suite")
        print("Documentation: https://developers.google.com/maps/documentation/places/web-service/place-details")
        print()
        
        # Test environment setup
        await self.run_test("Environment Setup", self.test_environment_setup)
        
        print("\n🚀 Starting Google Places API Tests...")
        print("=" * 60)
        
        # Core functionality tests
        print("\n🔍 Testing Place Details...")
        await self.run_test("Place Details - Basic", self.test_place_details_basic)
        await self.run_test("Place Details - With Fields", self.test_place_details_with_fields)
        await self.run_test("Place Details - With Language", self.test_place_details_with_language)
        
        print("\n📸 Testing Place Photos...")
        await self.run_test("Place Photos Info", self.test_place_photos_info)
        await self.run_test("Place Photo Download", self.test_place_photo_download)
        
        print("\n📋 Testing Utility Functions...")
        await self.run_test("Place Summary", self.test_place_summary)
        await self.run_test("Validation Functions", self.test_validation_functions)
        
        print("\n⚠️ Testing Error Handling...")
        await self.run_test("Error Handling", self.test_error_handling)
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate and display test summary"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results
        results_file = "googleplaces_test_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": success_rate
                },
                "results": self.results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to: {results_file}")

async def main():
    """Main test execution"""
    test_suite = GooglePlacesTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())