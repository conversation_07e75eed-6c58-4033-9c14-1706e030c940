import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

// Demo credentials for testing
const DEMO_CREDENTIALS = {
  "<EMAIL>": "demo123",
  "<EMAIL>": "test123",
  "<EMAIL>": "admin123"
};

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Check demo credentials
    if (DEMO_CREDENTIALS[email as keyof typeof DEMO_CREDENTIALS] === password) {
      // Set demo token cookie
      const response = NextResponse.json(
        { 
          success: true, 
          message: "Login successful",
          user: { email }
        },
        { status: 200 }
      );

      // Set authentication cookie
      response.cookies.set("demo_token", "demo_authenticated_user", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: "/"
      });

      return response;
    }

    // Invalid credentials
    return NextResponse.json(
      { error: "Invalid credentials" },
      { status: 401 }
    );

  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
