# API Integrated

1. Google Maps Javascript API
2. Google Places API
3. Amadeus API
4. Tomorrow.io API
5. Flight API
6. Geoapify API
7. Hotelbeds API
8. Open Weather Map API

---

# Script

- **test_api** folder
    1. API Integrated (2 - 8)
    2. Test Results

---

# Backend Update
1. Included API routes in **main_enhanced.py** (lines 110 - 118)
2. created **backend/app** folder

- **app folder**
    - **models** folder: models of API responses
    - **services** folder: logics of APIs
    - **routers** folder: router of APIs
    - **main_enhanced.py** file

---

# Frontend File Update

1. created simple **Map.tsx** to implement map component like imean
2. included ./Map in src/ui/ChatInterface.tsx