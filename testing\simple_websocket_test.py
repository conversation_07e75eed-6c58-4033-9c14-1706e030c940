#!/usr/bin/env python3
"""
Simple WebSocket test for speech recognition endpoint
"""
import asyncio
import websockets
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_simple():
    """Simple WebSocket connection test"""
    uri = "ws://localhost:8002/ws/speech"
    
    try:
        logger.info(f"Attempting to connect to {uri}")
        
        # Try to connect with a short timeout
        websocket = await asyncio.wait_for(
            websockets.connect(uri), 
            timeout=10.0
        )
        
        logger.info("✅ Successfully connected to WebSocket")
        
        # Send a small test message
        test_data = b"test"
        await websocket.send(test_data)
        logger.info("📤 Sent test data")
        
        # Wait for response with timeout
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            logger.info(f"📥 Received response: {response}")
        except asyncio.TimeoutError:
            logger.warning("⏰ No response received within 5 seconds (this is normal for speech endpoint)")
        
        await websocket.close()
        logger.info("🔌 Connection closed")
        
    except asyncio.TimeoutError:
        logger.error("❌ Connection timeout - backend may be slow or not responding")
    except ConnectionRefusedError:
        logger.error("❌ Connection refused - is the backend server running on port 8002?")
    except Exception as e:
        logger.error(f"❌ Connection failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_websocket_simple())