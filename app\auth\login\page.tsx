"use client";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginSuccess } from "@/store/authSlice";
import { Mail, Lock, ArrowRight, ShieldCheck, Rocket, Eye, EyeOff } from "lucide-react";
import Brand from "@/ui/Brand";

export default function LoginPage(){
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [agree, setAgree] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  async function onSubmit(e: React.FormEvent){
    e.preventDefault();
    if(!agree) return alert("Please agree to the Terms and Privacy Policy.");
    setLoading(true);
    const res = await fetch("/api/auth/login", {
      method: "POST",
      headers: { "Content-Type":"application/json" },
      body: JSON.stringify({ email, password })
    });
    setLoading(false);
    if(res.ok){
      dispatch(loginSuccess({ email }));
      router.push("/home");
    } else {
      alert("Invalid credentials");
    }
  }

  return (
    <div className="min-h-screen container-page grid md:grid-cols-2 items-center gap-8 py-12">
      {/* Left promo card */}
      <section className="card p-8">
        <h2 className="text-3xl md:text-4xl font-bold leading-tight">
          Unlock More Quota & Full Features! <span aria-hidden>🚀</span>
        </h2>
        <ul className="mt-6 space-y-3 text-gray-700">
          <li className="flex items-start gap-2"><ShieldCheck className="mt-0.5 text-my-success" /> Register now to boost more quota/day.</li>
          <li className="flex items-start gap-2"><ShieldCheck className="mt-0.5 text-my-success" /> Extra perks: permanent chat storage, faster data retrieval.</li>
        </ul>
        <div className="mt-6 rounded-2xl p-5 badge-gold border">
          <p className="text-sm text-gray-700">Designed for travelers in Malaysia — flights, hotels, foods and attractions at your fingertips.</p>
        </div>

        {/* Demo Credentials */}
        <div className="mt-4 rounded-xl p-4 bg-blue-50 border border-blue-200">
          <h4 className="text-sm font-semibold text-blue-800 mb-2">🔑 Demo Credentials</h4>
          <div className="text-xs text-blue-700 space-y-1">
            <div><strong>Email:</strong> <EMAIL></div>
            <div><strong>Password:</strong> demo123</div>
          </div>
        </div>
      </section>

      {/* Right auth form */}
      <section className="card p-8">
        <div className="flex items-center justify-between mb-4 header-border pb-4">
          <Brand />
        </div>
        <div className="flex items-center gap-2 text-2xl font-semibold mb-4">
          <span className="text-my-primary font-extrabold tracking-tight md:tracking-tighter2 text-2xl md:text-3xl">Welcome back</span>
        </div>

        <button type="button" className="btn btn-secondary w-full mb-4" aria-label="Continue with Google">
          Continue with Google
        </button>

        <div className="relative my-4 text-center">
          <span className="px-3 py-1 text-xs bg-white/80 rounded-full border">Or Continue with Email</span>
          <div className="absolute inset-x-0 top-1/2 -z-10 border-t border-dashed"></div>
        </div>

        <form onSubmit={onSubmit} className="space-y-3">
          <label className="block text-sm font-medium mb-1">Email</label>
          <div className="flex items-center gap-2">
            <Mail className="text-gray-400" />
            <input className="input" type="email" placeholder="<EMAIL>" value={email} onChange={e=>setEmail(e.target.value)} required />
          </div>

          <label className="block text-sm font-medium mb-1">Password</label>
          <div className="flex items-center gap-2 relative">
            <Lock className="text-gray-400" />
            <input 
              className="input pr-10" 
              type={showPassword ? "text" : "password"} 
              placeholder="••••••••" 
              value={password} 
              onChange={e=>setPassword(e.target.value)} 
              required 
            />
            <button
              type="button"
              className="absolute right-3 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={() => setShowPassword(!showPassword)}
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>

          <div className="flex items-center gap-2 mt-1 text-sm">
            <input id="agree" type="checkbox" checked={agree} onChange={()=>setAgree(!agree)} className="h-4 w-4 rounded border-gray-300"/>
            <label htmlFor="agree">I agree to the <a className="link" href="#">Terms</a> and <a className="link" href="#">Privacy Policy</a></label>
          </div>

          <button className="btn btn-primary w-full mt-4" disabled={loading} aria-label="Sign in">
            {loading ? "Signing in..." : <>Sign up / Sign in <ArrowRight className="ml-2" /></>}
          </button>
        </form>

        <div className="mt-4 flex justify-between text-sm">
          <Link className="link" href="/auth/register">Create account</Link>
          <Link className="link" href="/auth/forgot">Forgot password?</Link>
        </div>
      </section>
    </div>
  );
}
