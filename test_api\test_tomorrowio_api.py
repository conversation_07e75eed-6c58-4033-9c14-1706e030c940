#!/usr/bin/env python3
"""
Tomorrow.io API Test Script - Realtime Weather Only

This script tests the Tomorrow.io Realtime Weather API integration using the updated
service layer and models. It demonstrates the realtime weather endpoint and validates
the response structure against our Pydantic models.

Documentation: https://docs.tomorrow.io/reference/realtime-weather
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.tomorrowio_service import (
    get_realtime_weather,
    validate_location,
    format_location_for_api,
    get_field_validation
)
from app.models.tomorrowio_model import (
    RealtimeWeatherResponse,
    Units,
    WeatherFields,
    get_weather_description,
    calculate_comfort_score
)

class TomorrowIOAPITester:
    """Test class for Tomorrow.io Realtime Weather API"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_realtime_weather(self):
        """Test realtime weather endpoint"""
        try:
            print("\n🌤️ Testing Realtime Weather...")
            
            # Test basic realtime weather (London)
            response = await get_realtime_weather(
                location="London, UK",
                fields=["temperature", "humidity", "windSpeed", "weatherCode"],
                units="metric"
            )
            
            # Validate response structure
            if "data" in response:
                data = response["data"]
                values = data.get("values", {})
                
                self.log_test(
                    "Realtime Weather - London",
                    "temperature" in values,
                    {
                        "location": response.get("location", {}).get("name"),
                        "temperature": values.get("temperature"),
                        "humidity": values.get("humidity"),
                        "wind_speed": values.get("windSpeed"),
                        "weather_code": values.get("weatherCode")
                    }
                )
                
                # Test required fields
                required_fields = ["temperature", "humidity", "windSpeed", "weatherCode"]
                missing_fields = [field for field in required_fields if field not in values]
                
                self.log_test(
                    "Realtime Weather - Required Fields",
                    len(missing_fields) == 0,
                    {
                        "missing_fields": missing_fields,
                        "available_fields": list(values.keys())
                    }
                )
            else:
                self.log_test("Realtime Weather - London", False, {"error": "Missing data field"})
            
            # Test with coordinates
            coord_response = await get_realtime_weather(
                location="40.7128,-74.0060",  # New York coordinates
                fields=["temperature", "precipitationIntensity", "cloudCover"],
                units="imperial"
            )
            
            self.log_test(
                "Realtime Weather - Coordinates",
                "data" in coord_response,
                {
                    "location": "New York (coordinates)",
                    "temperature": coord_response.get("data", {}).get("values", {}).get("temperature"),
                    "units": "imperial"
                }
            )
            
            # Test with all default fields
            full_response = await get_realtime_weather(
                location="Paris, France",
                units="metric"
            )
            
            if "data" in full_response:
                values = full_response["data"]["values"]
                default_fields = [
                    "temperature", "temperatureApparent", "humidity", "windSpeed", 
                    "windDirection", "precipitationIntensity", "precipitationProbability",
                    "weatherCode", "cloudCover", "uvIndex", "visibility"
                ]
                available_default_fields = [field for field in default_fields if field in values]
                
                self.log_test(
                    "Realtime Weather - Default Fields",
                    len(available_default_fields) >= 8,  # At least 8 fields should be available
                    {
                        "location": "Paris, France",
                        "available_fields": len(available_default_fields),
                        "total_default_fields": len(default_fields),
                        "fields": available_default_fields
                    }
                )
            else:
                self.log_test("Realtime Weather - Default Fields", False, {"error": "Missing data field"})
            
        except Exception as e:
            self.log_test("Realtime Weather API", False, {"error": str(e)})
    
    async def test_utility_functions(self):
        """Test utility functions"""
        try:
            print("\n🔧 Testing Utility Functions...")
            
            # Test location validation with more realistic expectations
            test_cases = [
                ("London, UK", True),           # City with country
                ("40.7128,-74.0060", True),    # Valid coordinates
                ("New York, NY", True),        # City with state
                ("", False),                   # Empty string should be invalid
                ("Paris", True),               # Simple city name
                ("999,-999", False),           # Invalid coordinates (out of range)
            ]
            
            location_tests = []
            for location, expected in test_cases:
                result = validate_location(location)
                location_tests.append(result == expected)
            
            utility_success = all(location_tests)
            self.log_test(
                "Location Validation",
                utility_success,
                {
                    "test_cases": len(test_cases),
                    "passed": sum(location_tests),
                    "all_tests_passed": utility_success
                }
            )
            
            # Test location formatting
            test_location = "London, UK"
            formatted = format_location_for_api(test_location)
            format_success = isinstance(formatted, str) and len(formatted) > 0
            self.log_test(
                "Location Formatting",
                format_success,
                {
                    "original": test_location,
                    "formatted": formatted
                }
            )
            
            # Test field validation
            test_fields = ["temperature", "humidity", "invalidField"]
            validated_fields = get_field_validation(test_fields)
            field_success = len(validated_fields) >= 2  # Should filter out invalid field
            self.log_test(
                "Field Validation",
                field_success,
                {
                    "input_fields": test_fields,
                    "validated_fields": validated_fields,
                    "filtered_count": len(validated_fields)
                }
            )
            
        except Exception as e:
            self.log_test("Utility Functions", False, {"error": str(e)})
    
    async def test_model_functions(self):
        """Test model functions"""
        try:
            print("\n📊 Testing Model Functions...")
            
            # Test weather description
            weather_code = 1000  # Clear sky
            description = get_weather_description(weather_code)
            desc_success = isinstance(description, str) and len(description) > 0
            self.log_test(
                "Weather Description",
                desc_success,
                {
                    "weather_code": weather_code,
                    "description": description
                }
            )
            
            # Test comfort score calculation
            comfort_score = calculate_comfort_score(22, 60, 5)  # temperature, humidity, wind_speed
            comfort_success = isinstance(comfort_score, (int, float)) and 0 <= comfort_score <= 10
            self.log_test(
                "Comfort Score Calculation",
                comfort_success,
                {
                    "temperature": 22,
                    "humidity": 60,
                    "wind_speed": 5,
                    "comfort_score": comfort_score
                }
            )
            
        except Exception as e:
            self.log_test("Model Functions", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid location
            try:
                await get_realtime_weather(
                    location="InvalidLocation12345",
                    fields=["temperature"]
                )
                self.log_test("Error Handling - Invalid Location", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Location", True, {"expected_error": str(e)})
            
            # Test invalid fields
            try:
                await get_realtime_weather(
                    location="London, UK",
                    fields=["invalidField123"]
                )
                # This might not fail at API level, so we test field validation separately
                invalid_fields = get_field_validation(["invalidField123"])
                self.log_test("Error Handling - Invalid Fields", len(invalid_fields) == 0, {"filtered_fields": invalid_fields})
            except Exception as e:
                self.log_test("Error Handling - Invalid Fields", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Tomorrow.io Realtime Weather API Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_realtime_weather()
        await self.test_utility_functions()
        await self.test_model_functions()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("tomorrowio_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: tomorrowio_test_results.json")

async def main():
    """Main test runner"""
    print("🌤️ Tomorrow.io Realtime Weather API Test Suite")
    print("Documentation: https://docs.tomorrow.io/reference/realtime-weather")
    print()
    
    # Check environment variables
    required_env_vars = ["TOMORROW_IO_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('TOMORROW_IO_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = TomorrowIOAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())