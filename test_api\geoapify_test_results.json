{"summary": {"total": 19, "passed": 19, "failed": 0, "success_rate": 100.0}, "results": [{"test": "Location Search - Basic Query", "success": true, "timestamp": "2025-08-30T15:20:29.700274", "details": {"query": "restaurants in Paris", "results_count": 5, "first_result": "City Hall Oasis", "has_coordinates": true}}, {"test": "Location Search - Response Structure", "success": true, "timestamp": "2025-08-30T15:20:29.700893", "details": {"missing_fields": [], "response_type": "FeatureCollection", "features_count": 5}}, {"test": "Location Search - Feature Structure", "success": true, "timestamp": "2025-08-30T15:20:29.701896", "details": {"missing_fields": [], "geometry_type": "Point", "has_coordinates": true}}, {"test": "Location Search - With Categories", "success": true, "timestamp": "2025-08-30T15:20:31.086356", "details": {"query": "hotels in London", "category": "accommodation", "results_count": 3}}, {"test": "Location Search - With Bias Location", "success": true, "timestamp": "2025-08-30T15:20:45.369989", "details": {"query": "coffee shops", "bias_location": "Paris (48.8566, 2.3522)", "results_count": 3}}, {"test": "Nearby Search - Times Square", "success": true, "timestamp": "2025-08-30T15:20:45.928406", "details": {"location": "Times Square, NYC", "radius": "500m", "categories": ["commercial", "catering"], "results_count": 5}}, {"test": "Nearby Search - Proximity Check", "success": true, "timestamp": "2025-08-30T15:20:45.929333", "details": {"search_center": [40.758, -73.9855], "result_coords": [40.7577545503642, -73.98761601214207], "lat_diff": 0.00024544963580552803, "lon_diff": 0.0021160121420678024}}, {"test": "Nearby Search - Large Radius", "success": true, "timestamp": "2025-08-30T15:20:49.009194", "details": {"location": "London", "radius": "2000m", "results_count": 10}}, {"test": "Location Details - Valid Place ID", "success": true, "timestamp": "2025-08-30T15:20:50.277530", "details": {"place_id": "51f0a38e3f778052c0596cb2b3043e5b4440f00102f901f1c2003b0000000092030f436974792048616c6c204f61736973", "name": "City Hall Oasis", "formatted_address": "City Hall Oasis, Broadway, New York, NY 10003, United States of America", "has_coordinates": true, "available_fields": ["feature_type", "name", "building", "commercial", "categories", "datasource", "street", "city", "county", "state"]}}, {"test": "Location Details - Essential Fields", "success": true, "timestamp": "2025-08-30T15:20:50.277881", "details": {"essential_fields": ["name", "formatted"], "available_essential": ["name", "formatted"], "coverage": "2/2"}}, {"test": "Autocomplete - Basic Query", "success": true, "timestamp": "2025-08-30T15:20:59.069514", "details": {"query": "New Y", "suggestions_count": 5, "first_suggestion": "Contiguous United States"}}, {"test": "Autocomplete - Relevance Check", "success": true, "timestamp": "2025-08-30T15:20:59.070521", "details": {"total_suggestions": 5, "relevant_suggestions": 4, "relevance_ratio": "4/5"}}, {"test": "Autocomplete - With Bias", "success": true, "timestamp": "2025-08-30T15:21:05.528776", "details": {"query": "Central Park", "bias_location": "NYC", "suggestions_count": 3}}, {"test": "Coordinate Validation", "success": true, "timestamp": "2025-08-30T15:21:05.529776", "details": {"test_cases": 5, "passed": 5, "all_passed": true}}, {"test": "Category Validation", "success": true, "timestamp": "2025-08-30T15:21:05.530854", "details": {"input_categories": ["accommodation", "commercial", "invalid_category", "catering"], "validated_categories": ["accommodation", "commercial", "catering"], "filtered_count": 3}}, {"test": "Place Types Enum", "success": true, "timestamp": "2025-08-30T15:21:05.530854", "details": {"available_types": 10, "sample_types": ["accommodation", "commercial", "catering", "entertainment", "tourism"]}}, {"test": "Error Handling - Invalid Coordinates", "success": true, "timestamp": "2025-08-30T15:21:05.533495", "details": {"expected_error": "Invalid coordinates"}}, {"test": "Error <PERSON> - Invalid Place ID", "success": true, "timestamp": "2025-08-30T15:21:06.346020", "details": {"expected_error": "API error: 400"}}, {"test": "Error <PERSON>ling - Empty Query", "success": true, "timestamp": "2025-08-30T15:21:06.347019", "details": {"expected_error": "Search text cannot be empty"}}]}