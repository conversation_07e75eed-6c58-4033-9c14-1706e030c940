import { Mic } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";

// 环境变量配置
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8002';

interface WebSocketMessage {
    type: "transcript" | "response" | "error";
    transcript?: string;
    is_final?: boolean;
    detected_language?: string;
    confidence?: number;
    response?: string;
    timestamp?: string;
    error?: string;
}

interface VoiceRecorderProps {
    onTranscriptChange?: (transcript: string, isFinal: boolean) => void;
    className?: string;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({ onTranscriptChange, className }) => {
    const [transcript, setTranscript] = useState<string>("");
    const [detectedLang, setDetectedLang] = useState<string>("");
    const [isRecording, setIsRecording] = useState<boolean>(false);
    const wsRef = useRef<WebSocket | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const processorRef = useRef<AudioWorkletNode | null>(null);
    const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
    const streamRef = useRef<MediaStream | null>(null);

    useEffect(() => {
        const clientId = `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const ws = new WebSocket(`${WS_BASE_URL}/ws/speech/${clientId}`);

        ws.onopen = () => {
            console.log("WebSocket connected successfully to ws://localhost:8002/ws/speech");
        };

        ws.onmessage = (event: MessageEvent) => {
            try {
                const data: WebSocketMessage = JSON.parse(event.data);

                if (data.type === "transcript") {
                    setTranscript(data.transcript || "");
                    setDetectedLang(data.detected_language || "");

                    if (onTranscriptChange && data.transcript) {
                        onTranscriptChange(data.transcript, data.is_final || false);
                    }
                } else if (data.type === "response") {
                    console.log("Received Gemini response:", data.response);
                } else if (data.type === "error") {
                    console.error("Received error:", data.error);
                }
            } catch (error) {
                console.error("Error parsing WebSocket message:", error);
            }
        };

        ws.onerror = (error) => {
            console.error("WebSocket error:", error);
        };

        ws.onclose = (event) => {
            console.log("WebSocket disconnected. Code:", event.code, "Reason:", event.reason);
            console.log("Was clean close:", event.wasClean);
        };

        wsRef.current = ws;

        // 返回清理函数
        return () => {
            // 清理WebSocket连接
            if (wsRef.current) {
                wsRef.current.close();
                wsRef.current = null;
            }

            // 清理音频资源
            stopRecording();
        };
    }, [onTranscriptChange]);

    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            const audioContext = new AudioContext({ sampleRate: 16000 });

            await audioContext.audioWorklet.addModule('/audio-processor.js');

            const source = audioContext.createMediaStreamSource(stream);
            const workletNode = new AudioWorkletNode(audioContext, 'audio-processor');

            // Handle audio data from worklet
            workletNode.port.onmessage = (event) => {
                const buffer = event.data;
                if (wsRef.current?.readyState === WebSocket.OPEN) {
                    wsRef.current.send(buffer);
                }
            };

            source.connect(workletNode);

            // Save references
            audioContextRef.current = audioContext;
            sourceRef.current = source;
            processorRef.current = workletNode; // Store worklet node
            streamRef.current = stream;
            setIsRecording(true);
        } catch (error) {
            console.error("Error starting recording:", error);
        }
    };

    const stopRecording = () => {
        try {
            // 安全地清理AudioWorkletNode
            if (processorRef.current) {
                processorRef.current.disconnect();
                processorRef.current = null;
            }

            // 安全地清理MediaStreamAudioSourceNode
            if (sourceRef.current) {
                sourceRef.current.disconnect();
                sourceRef.current = null;
            }

            // 安全地关闭AudioContext
            if (audioContextRef.current) {
                if (audioContextRef.current.state !== 'closed') {
                    audioContextRef.current.close();
                }
                audioContextRef.current = null;
            }

            // 停止所有媒体轨道
            if (streamRef.current) {
                streamRef.current.getTracks().forEach((track) => {
                    track.stop();
                });
                streamRef.current = null;
            }

            setIsRecording(false);
            console.log("Audio recording stopped and resources cleaned up");
        } catch (error) {
            console.error("Error stopping recording:", error);
            setIsRecording(false);
        }
    };

    const toggleRecording = () => {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    };

    return (
        <button
            onClick={toggleRecording}
            className={`p-2 rounded-lg transition-all duration-200 ${className || ''} ${isRecording
                ? "text-red-500 bg-red-50 hover:bg-red-100"
                : "text-gray-500 hover:text-my-primary hover:bg-my-blue-50"
                }`}
            aria-label={isRecording ? "Stop recording" : "Start recording"}
            title={isRecording ? "停止录音" : "语音输入"}
        >
            {isRecording ? (
                <span className="text-red-500">🔴</span>
            ) : (
                <Mic className="w-5 h-5" />
            )}
        </button>
    );
};

export default VoiceRecorder;
