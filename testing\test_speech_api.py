#!/usr/bin/env python3
"""
测试Google Cloud Speech-to-Text API密钥功能
"""

import os
import base64
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv("backend/.env")

def test_speech_api_with_key():
    """测试使用API密钥的语音识别"""
    
    api_key = os.getenv('GOOGLE_SPEECH_API_KEY')
    if not api_key:
        print("❌ 未找到GOOGLE_SPEECH_API_KEY环境变量")
        return False
    
    print(f"🔑 使用API密钥: {api_key[:10]}...")
    
    # 创建测试音频数据（静音）
    # 16kHz, 16-bit, mono, 1秒
    sample_rate = 16000
    duration = 1  # 1秒
    audio_data = b'\x00' * (sample_rate * 2 * duration)  # 16-bit = 2 bytes per sample
    
    # 编码为base64
    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
    
    # 准备请求数据
    request_data = {
        "config": {
            "encoding": "LINEAR16",
            "sampleRateHertz": 16000,
            "languageCode": "en-US",
            "enableAutomaticPunctuation": True,
            "model": "latest_long"
        },
        "audio": {
            "content": audio_base64
        }
    }
    
    # 发送请求
    url = f"https://speech.googleapis.com/v1/speech:recognize?key={api_key}"
    
    try:
        print("🚀 发送语音识别请求...")
        response = requests.post(url, json=request_data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"📝 响应内容: {json.dumps(result, indent=2)}")
            
            if "results" in result and result["results"]:
                print("🎯 检测到语音结果")
                for i, speech_result in enumerate(result["results"]):
                    if "alternatives" in speech_result:
                        for j, alternative in enumerate(speech_result["alternatives"]):
                            transcript = alternative.get("transcript", "")
                            confidence = alternative.get("confidence", 0.0)
                            print(f"  结果 {i+1}.{j+1}: '{transcript}' (置信度: {confidence})")
            else:
                print("ℹ️ 静音音频，无语音内容检测到（这是正常的）")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_api_key_validation():
    """测试API密钥验证"""
    print("🔍 测试API密钥验证...")
    
    # 使用无效的API密钥测试
    invalid_key = "invalid_key_test"
    url = f"https://speech.googleapis.com/v1/speech:recognize?key={invalid_key}"
    
    request_data = {
        "config": {
            "encoding": "LINEAR16",
            "sampleRateHertz": 16000,
            "languageCode": "en-US"
        },
        "audio": {
            "content": base64.b64encode(b'\x00' * 1000).decode('utf-8')
        }
    }
    
    try:
        response = requests.post(url, json=request_data, timeout=10)
        if response.status_code == 400 or response.status_code == 403:
            print("✅ API密钥验证正常工作（拒绝无效密钥）")
            return True
        else:
            print(f"⚠️ 意外的响应状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Google Cloud Speech-to-Text API密钥测试")
    print("=" * 50)
    
    results = []
    
    # 测试API密钥验证
    results.append(("API密钥验证", test_api_key_validation()))
    
    # 测试实际API调用
    results.append(("API密钥功能", test_speech_api_with_key()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！API密钥配置正确。")
        print("\n📋 下一步:")
        print("  1. 启动后端服务: cd backend && python main_enhanced.py")
        print("  2. 测试WebSocket连接")
        print("  3. 测试语音识别功能")
    else:
        print("⚠️ 部分测试失败，请检查:")
        print("  1. API密钥是否正确")
        print("  2. 网络连接是否正常")
        print("  3. Google Cloud Speech API是否已启用")
    
    return all_passed

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)
