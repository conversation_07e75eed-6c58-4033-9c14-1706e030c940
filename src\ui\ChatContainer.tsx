"use client";
import { useState, useCallback, useEffect } from "react";
import ChatInterface, { type Message } from "./ChatInterface";
import ChatBarV2 from "./ChatBarV2";
import { saveChatSession, updateChatSession, loadChatSession } from "@/utils/chatHistory";

export default function ChatContainer() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    setMessages(prev => {
      const newMessages = prev.map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      );
      
      // 如果有当前会话ID，更新会话
      if (currentSessionId && newMessages.length > 0) {
        updateChatSession(currentSessionId, newMessages);
      }
      
      return newMessages;
    });
  }, [currentSessionId]);

  // 监听消息变化，自动保存聊天记录
  useEffect(() => {
    if (messages.length > 0) {
      // 如果没有当前会话ID，创建新会话
      if (!currentSessionId) {
        const sessionId = saveChatSession(messages);
        setCurrentSessionId(sessionId);
      } else {
        // 更新现有会话
        updateChatSession(currentSessionId, messages);
      }
    }
  }, [messages, currentSessionId]);

  // 加载特定聊天会话
  const loadChatSessionMessages = useCallback((sessionId: string) => {
    const sessionMessages = loadChatSession(sessionId);
    setMessages(sessionMessages);
    setCurrentSessionId(sessionId);
  }, []);

  // 开始新对话
  const startNewChat = useCallback(() => {
    setMessages([]);
    setCurrentSessionId(null);
    setIsLoading(false);
  }, []);

  // 暴露方法给全局事件监听器
  useEffect(() => {
    const handleLoadSession = (event: CustomEvent) => {
      if (event.detail?.sessionId) {
        loadChatSessionMessages(event.detail.sessionId);
      }
    };

    const handleNewChat = () => {
      startNewChat();
    };

    window.addEventListener('syntour.loadChatSession' as any, handleLoadSession);
    window.addEventListener('syntour.newChat' as any, handleNewChat);

    return () => {
      window.removeEventListener('syntour.loadChatSession' as any, handleLoadSession);
      window.removeEventListener('syntour.newChat' as any, handleNewChat);
    };
  }, [loadChatSessionMessages, startNewChat]);

  return (
    <div className="w-full max-w-5xl mx-auto">
      {/* Seamless Chat Interface */}
      <div className="relative">
        {/* Chat Messages Area - Always visible, expands as needed */}
        <div className={`transition-all duration-500 ease-in-out ${
          messages.length > 0 || isLoading
            ? 'min-h-[500px] max-h-[600px] mb-6 opacity-100'
            : 'h-0 mb-0 opacity-0'
        }`}>
          {(messages.length > 0 || isLoading) && (
            <div className="h-full rounded-2xl overflow-hidden backdrop-blur-sm">
              <ChatInterface
                messages={messages}
                isLoading={isLoading}
                onUpdateMessage={updateMessage}
              />
            </div>
          )}
        </div>

        {/* Chat Input - Always visible */}
        <div className="relative z-10">
          <ChatBarV2
            messages={messages}
            setMessages={setMessages}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        </div>
      </div>
    </div>
  );
}