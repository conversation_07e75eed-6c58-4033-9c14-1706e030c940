"use client";
import { useState, useEffect } from "react";
import { PlusCircle, History } from "lucide-react";
import PlannerWizard from "./PlannerWizard";
import { useToast, ToastContainer } from "./Toast";

export default function RightDockV2() {
  const [open, setOpen] = useState(false);
  const [synTourPressed, setSynTourPressed] = useState(false);
  const [newChatPressed, setNewChatPressed] = useState(false);
  const [historyPressed, setHistoryPressed] = useState(false);
  const { toasts, showToast, removeToast } = useToast();

  useEffect(() => {
    function openViaEvent(){ setOpen(true); }
    function closeViaEvent(){ setOpen(false); }
    window.addEventListener("mytour.open_planner", openViaEvent);
    window.addEventListener("mytour.close_planner", closeViaEvent);
    return () => {
      window.removeEventListener("mytour.open_planner", openViaEvent);
      window.removeEventListener("mytour.close_planner", closeViaEvent);
    };
  }, [setOpen]);

  function handlePlannerSubmit(answers: any) {
    console.log("Planner answers:", answers);
    // The PlannerWizard will automatically fill the chat input
  }

  return (
    <>
      {/* floating dock on the left */}
      <div className="fixed left-4 bottom-28 flex flex-col gap-3 z-40">
        {/* SynTour icon - 增强交互效果 */}
        <button
          className={`w-16 h-16 rounded-2xl text-white shadow-soft overflow-hidden hover:scale-105 active:scale-95 transition-all duration-200 ${synTourPressed ? 'brightness-110 shadow-lg' : ''}`}
          aria-label="SynTour"
          style={{ 
            background: synTourPressed 
              ? "linear-gradient(90deg, #0066FF, #FFD700)" 
              : "linear-gradient(90deg, #010066, #FFCC00)" 
          }}
          onMouseDown={() => setSynTourPressed(true)}
          onMouseUp={() => setSynTourPressed(false)}
          onMouseLeave={() => setSynTourPressed(false)}
          onClick={() => {
            window.dispatchEvent(new Event("mytour.open_planner"));
            // 添加短暂的视觉反馈
            setSynTourPressed(true);
            setTimeout(() => setSynTourPressed(false), 150);
          }}
          title="Open SynTour"
        >
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src="/branding/syntour.png"
            onError={(e) => { 
              const target = e.currentTarget as HTMLImageElement;
              if (target.src !== "/branding/syntour-placeholder.svg") {
                target.src = "/branding/syntour-placeholder.svg";
              }
            }}
            alt="SynTour"
            className="w-full h-full object-cover"
          />
        </button>

        {/* New chat - 增强交互效果 */}
        <button
          className={`w-12 h-12 rounded-2xl bg-white border hover:bg-gray-50 hover:scale-105 active:scale-95 transition-all duration-200 ${newChatPressed ? 'bg-blue-50 border-blue-300 shadow-md' : ''}`}
          aria-label="New chat"
          onMouseDown={() => setNewChatPressed(true)}
          onMouseUp={() => setNewChatPressed(false)}
          onMouseLeave={() => setNewChatPressed(false)}
          onClick={() => {
            showToast({
              title: "🚧 Feature Under Maintenance",
              message: "New chat functionality is currently being developed. Please try again later!",
              variant: "info"
            });
            // 添加短暂的视觉反馈
            setNewChatPressed(true);
            setTimeout(() => setNewChatPressed(false), 150);
          }}
          title="New chat"
        >
          <PlusCircle className="mx-auto" />
        </button>

        {/* History - 增强交互效果 */}
        <button
          className={`w-12 h-12 rounded-2xl bg-white border hover:bg-gray-50 hover:scale-105 active:scale-95 transition-all duration-200 ${historyPressed ? 'bg-green-50 border-green-300 shadow-md' : ''}`}
          aria-label="History"
          onMouseDown={() => setHistoryPressed(true)}
          onMouseUp={() => setHistoryPressed(false)}
          onMouseLeave={() => setHistoryPressed(false)}
          onClick={() => {
            showToast({
              title: "🚧 Feature Under Maintenance",
              message: "Chat history functionality is currently being developed. Please try again later!",
              variant: "info"
            });
            // 添加短暂的视觉反馈
            setHistoryPressed(true);
            setTimeout(() => setHistoryPressed(false), 150);
          }}
          title="History"
        >
          <History className="mx-auto" />
        </button>
      </div>

      {/* Full overlay planner */}
      {open && (
        <>
          {/* Backdrop blocks interactions below */}
          <div
            className="fixed inset-0 z-[60] bg-black/35 backdrop-blur-[2px]"
            onClick={() => setOpen(false)}
            aria-hidden="true"
          />
          {/* Panel */}
          <aside
            role="dialog"
            aria-modal="true"
            className="fixed inset-y-0 right-0 z-[9999] w-1/4 bg-white shadow-2xl border-l border-gray-200 overflow-hidden"
          >
            <div className="h-full flex flex-col">
              {/* 头部 */}
              <div className="flex-shrink-0 bg-white border-b p-4 flex items-center justify-between">
                <h2 className="text-xl font-bold text-my-primary">SynTour Planner</h2>
                <button 
                  onClick={() => setOpen(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl leading-none"
                  aria-label="Close planner"
                >
                  ×
                </button>
              </div>
              {/* 内容区域 */}
              <div className="flex-1 overflow-y-auto">
                <PlannerWizard onSubmit={handlePlannerSubmit} />
              </div>
            </div>
          </aside>
        </>
      )}
      
      {/* Toast通知 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </>
  );
}
