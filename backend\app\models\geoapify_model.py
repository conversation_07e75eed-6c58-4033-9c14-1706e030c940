# app/models/geoapify_model.py
from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum

class GeoapifyError(Exception):
    """Custom exception for Geoapify API errors"""
    def __init__(self, message: str, status_code: int = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class PlaceType(str, Enum):
    """Place types for filtering search results"""
    ACCOMMODATION = "accommodation"
    COMMERCIAL = "commercial"
    CATERING = "catering"
    ENTERTAINMENT = "entertainment"
    TOURISM = "tourism"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    SPORT = "sport"
    SERVICE = "service"
    OFFICE = "office"

class LocationGeometry(BaseModel):
    """Geometry information for a location"""
    type: str = Field(..., description="Geometry type (usually 'Point')")
    coordinates: List[float] = Field(..., description="[longitude, latitude] coordinates")

class LocationProperties(BaseModel):
    """Essential properties of a location"""
    name: Optional[str] = Field(None, description="Place name")
    formatted: Optional[str] = Field(None, description="Formatted address")
    address_line1: Optional[str] = Field(None, description="Primary address line")
    address_line2: Optional[str] = Field(None, description="Secondary address line")
    city: Optional[str] = Field(None, description="City name")
    state: Optional[str] = Field(None, description="State/province")
    country: Optional[str] = Field(None, description="Country name")
    country_code: Optional[str] = Field(None, description="ISO country code")
    postcode: Optional[str] = Field(None, description="Postal code")
    place_id: Optional[str] = Field(None, description="Unique place identifier")
    categories: Optional[List[str]] = Field(None, description="Place categories")
    
    # Contact information
    phone: Optional[str] = Field(None, description="Phone number")
    website: Optional[str] = Field(None, description="Website URL")
    
    # Additional useful info
    opening_hours: Optional[str] = Field(None, description="Opening hours")
    rating: Optional[float] = Field(None, description="Rating score")

class LocationFeature(BaseModel):
    """A location feature from the API response"""
    type: str = Field(..., description="Feature type")
    geometry: LocationGeometry = Field(..., description="Location geometry")
    properties: LocationProperties = Field(..., description="Location properties")

class LocationSearchResponse(BaseModel):
    """Response model for location search"""
    type: str = Field(..., description="Response type")
    features: List[LocationFeature] = Field(..., description="List of location features")

class LocationDetailsProperties(BaseModel):
    """Detailed properties of a specific location"""
    name: Optional[str] = Field(None, description="Place name")
    formatted: Optional[str] = Field(None, description="Formatted address")
    address_line1: Optional[str] = Field(None, description="Primary address line")
    address_line2: Optional[str] = Field(None, description="Secondary address line")
    city: Optional[str] = Field(None, description="City name")
    state: Optional[str] = Field(None, description="State/province")
    country: Optional[str] = Field(None, description="Country name")
    country_code: Optional[str] = Field(None, description="ISO country code")
    postcode: Optional[str] = Field(None, description="Postal code")
    place_id: Optional[str] = Field(None, description="Unique place identifier")
    categories: Optional[List[str]] = Field(None, description="Place categories")
    
    # Contact and business information
    phone: Optional[str] = Field(None, description="Phone number")
    website: Optional[str] = Field(None, description="Website URL")
    email: Optional[str] = Field(None, description="Email address")
    
    # Business details
    opening_hours: Optional[str] = Field(None, description="Opening hours")
    rating: Optional[float] = Field(None, description="Rating score")
    description: Optional[str] = Field(None, description="Place description")
    
    # Additional metadata
    brand: Optional[str] = Field(None, description="Brand name")
    operator: Optional[str] = Field(None, description="Operator name")

class LocationDetailsFeature(BaseModel):
    """A detailed location feature"""
    type: str = Field(..., description="Feature type")
    geometry: LocationGeometry = Field(..., description="Location geometry")
    properties: LocationDetailsProperties = Field(..., description="Detailed location properties")

class LocationDetailsResponse(BaseModel):
    """Response model for location details"""
    type: str = Field(..., description="Response type")
    features: List[LocationDetailsFeature] = Field(..., description="List of detailed location features")

# Utility functions
def extract_coordinates(feature: LocationFeature) -> tuple[float, float]:
    """Extract latitude and longitude from a location feature"""
    coords = feature.geometry.coordinates
    return coords[1], coords[0]  # Return (lat, lon)

def format_address(properties: LocationProperties) -> str:
    """Format a readable address from location properties"""
    parts = []
    
    if properties.address_line1:
        parts.append(properties.address_line1)
    if properties.address_line2:
        parts.append(properties.address_line2)
    if properties.city:
        parts.append(properties.city)
    if properties.state:
        parts.append(properties.state)
    if properties.postcode:
        parts.append(properties.postcode)
    if properties.country:
        parts.append(properties.country)
    
    return ", ".join(parts) if parts else properties.formatted or "Unknown Address"

def is_business_location(properties: LocationProperties) -> bool:
    """Check if a location is a business/commercial location"""
    if not properties.categories:
        return False
    
    business_categories = [
        "accommodation", "commercial", "catering", "entertainment", 
        "tourism", "healthcare", "education", "sport"
    ]
    
    return any(cat in business_categories for cat in properties.categories)