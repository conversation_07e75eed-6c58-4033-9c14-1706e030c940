from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, UploadFile, File, Form, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from app.routers import hotelbeds_content
from pydantic import BaseModel, Field
import uvicorn

from typing import Optional, List, Dict, Any
import os
from dotenv import load_dotenv
from datetime import datetime
import json
import logging
import asyncio
import time
import uuid
import tempfile
import mimetypes
import base64
from enum import Enum
import aiohttp

from google.cloud import storage, speech
from google.cloud.speech_v1 import SpeechClient
from google.cloud.speech_v1.types import (
    RecognitionConfig,
    StreamingRecognitionConfig,
    StreamingRecognizeRequest
)
from google.oauth2 import service_account
from google import genai

from services.prompt_templates import PromptTemplates
from services.file_processor import FileProcessor

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants for file processing
MAX_FILE_SIZE_MB = 10
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# Error handling classes
class ErrorCode(str, Enum):
    VALIDATION_ERROR = "VALIDATION_ERROR"
    PROCESSING_ERROR = "PROCESSING_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    WEBSOCKET_ERROR = "WEBSOCKET_ERROR"
    FILE_ERROR = "FILE_ERROR"

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[ErrorCode] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    details: Optional[Dict[str, Any]] = None

# Tourism chatbot prompt
TOURISM_PROMPT = """You are SynTour, an advanced AI tourism assistant designed to provide comprehensive, personalized travel guidance. Your expertise spans global destinations, cultural insights, practical travel advice, and real-time assistance.

Core Capabilities:
- Destination recommendations based on preferences, budget, and travel style
- Cultural insights and local customs guidance
- Practical travel planning (visas, weather, transportation, accommodation)
- Real-time assistance during travel
- Multimodal interaction (text, voice, images)
- Multilingual support with cultural sensitivity

Personality:
- Enthusiastic and knowledgeable about travel
- Culturally sensitive and respectful
- Practical and helpful
- Friendly and approachable
- Adaptable to different communication styles

Response Guidelines:
- Provide specific, actionable advice
- Include relevant details (costs, timing, locations)
- Suggest alternatives when appropriate
- Be culturally sensitive and respectful
- Ask clarifying questions when needed
- Offer both popular and off-the-beaten-path options

Always aim to inspire and facilitate amazing travel experiences while ensuring safety and cultural respect."""

app = FastAPI(
    title="SynTour AI API - Enhanced",
    description="Advanced FastAPI backend for SynTour travel planning with multimodal AI support",
    version="2.0.0",
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3007",
        "http://localhost:3008",
        "http://localhost:3009",
        "http://localhost:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
from app.routers import amadeus_content, hotelbeds_content, flightapi_content, openweathermap_content, tomorrowio_content, geoapify_content, googleplaces_content
app.include_router(hotelbeds_content.router)
app.include_router(amadeus_content.router)
app.include_router(flightapi_content.router)
app.include_router(openweathermap_content.router)
app.include_router(tomorrowio_content.router)
app.include_router(geoapify_content.router)
app.include_router(googleplaces_content.router)

GCS_UPLOAD_THRESHOLD_MB = 20  # 小于最大文件大小

# Error handling helper functions
def create_error_response(
    error_message: str,
    error_code: ErrorCode = ErrorCode.PROCESSING_ERROR,
    details: Optional[Dict[str, Any]] = None
) -> ErrorResponse:
    return ErrorResponse(
        error=error_message,
        error_code=error_code,
        details=details
    )

def create_websocket_error_message(
    error_message: str,
    error_code: ErrorCode = ErrorCode.WEBSOCKET_ERROR,
    client_id: str = ""
) -> str:
    error_data = {
        "type": "error",
        "error": error_message,
        "error_code": error_code.value,
        "timestamp": datetime.now().isoformat(),
        "client_id": client_id
    }
    return json.dumps(error_data)

# FileProcessor已从services.file_processor导入，无需重复定义

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                if websocket.client_state.name == "CONNECTED":
                    await websocket.send_text(message)
                else:
                    logger.warning(f"WebSocket not connected for client {client_id}")
                    self.disconnect(client_id)
            except WebSocketDisconnect:
                logger.info(f"Client {client_id} disconnected during message send")
                self.disconnect(client_id)
            except Exception as e:
                logger.error(f"Failed to send message to client {client_id}: {e}")
                self.disconnect(client_id)

manager = ConnectionManager()

class MultimodalRequest(BaseModel):
    message: str
    context: Optional[str] = None
    file_descriptions: Optional[dict] = None

class ChatRequest(BaseModel):
    message: str
    context: Optional[str] = None
    user_preferences: Optional[dict] = None

class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    model: str = "fine-tuned-gemini-travel"
    processing_info: Optional[dict] = None
    is_truncated: Optional[bool] = False
    continuation_id: Optional[str] = None

# Helper function to detect truncated responses
def is_response_truncated(response_text: str) -> bool:
    """Detect if a response appears to be truncated"""
    if not response_text:
        return False

    # Check for common truncation indicators
    truncation_indicators = [
        # Incomplete sentences
        response_text.endswith(('...', '..', '..')),
        # Ends mid-sentence without proper punctuation
        not response_text.strip().endswith(('.', '!', '?', '"', "'", ')', ']', '}')),
        # Ends with incomplete markdown
        response_text.count('```') % 2 != 0,
        response_text.count('**') % 2 != 0,
        response_text.count('*') % 2 != 0,
        # Ends with incomplete list item
        response_text.strip().endswith(('*', '-', '1.', '2.', '3.', '4.', '5.')),
        # Very long response that might hit token limit
        len(response_text) > 3500,
    ]

    return any(truncation_indicators)

# Store for continuation contexts
continuation_store = {}

# Initialize Google Cloud Speech client
def initialize_speech_client():
    try:
        # 优先使用API密钥方式
        api_key = os.getenv('GOOGLE_SPEECH_API_KEY')
        if api_key:
            logger.info("Using API key for Speech-to-Text")
            return "api_key_mode"  # 标记使用API密钥模式

        # 尝试使用服务账户凭据
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if credentials_path and os.path.exists(credentials_path):
            credentials = service_account.Credentials.from_service_account_file(credentials_path)
            client = speech.SpeechClient(credentials=credentials)
            logger.info("Speech client initialized with service account credentials")
            return client
        else:
            # 最后尝试默认凭据
            client = speech.SpeechClient()
            logger.info("Speech client initialized with default credentials")
            return client
    except Exception as e:
        logger.error(f"Failed to initialize speech client: {e}")
        return None

speech_client = initialize_speech_client()

# HTTP API方式的语音识别函数
async def recognize_speech_with_api_key(audio_data: bytes) -> dict:
    """使用API密钥通过HTTP请求进行语音识别"""
    try:
        import base64
        import aiohttp

        api_key = os.getenv('GOOGLE_SPEECH_API_KEY')
        if not api_key:
            raise ValueError("API key not found")

        # 准备请求数据
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        request_data = {
            "config": {
                "encoding": "LINEAR16",
                "sampleRateHertz": 16000,
                "languageCode": "en-US",
                "alternativeLanguageCodes": ["es-ES", "fr-FR", "de-DE", "it-IT", "pt-PT"],
                "enableAutomaticPunctuation": True,
                "model": "latest_long"
            },
            "audio": {
                "content": audio_base64
            }
        }

        # 发送HTTP请求
        url = f"https://speech.googleapis.com/v1/speech:recognize?key={api_key}"

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=request_data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Speech API error {response.status}: {error_text}")
                    return {"error": f"API error {response.status}"}

    except Exception as e:
        logger.error(f"Speech recognition with API key failed: {e}")
        return {"error": str(e)}

# API密钥模式的WebSocket处理函数
async def handle_speech_with_api_key(websocket: WebSocket, client_id: str):
    """使用API密钥处理语音识别"""
    try:
        audio_buffer = bytearray()
        last_recognition_time = time.time()
        recognition_interval = 3.0  # 每3秒识别一次

        while True:
            try:
                # 接收音频数据
                data = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.1)

                if len(data) > 0:
                    audio_buffer.extend(data)

                # 检查是否需要进行识别
                current_time = time.time()
                if (current_time - last_recognition_time >= recognition_interval and
                    len(audio_buffer) > 16000):  # 至少1秒的音频数据

                    # 进行语音识别
                    audio_data = bytes(audio_buffer)
                    result = await recognize_speech_with_api_key(audio_data)

                    if "results" in result and result["results"]:
                        for speech_result in result["results"]:
                            if "alternatives" in speech_result and speech_result["alternatives"]:
                                transcript = speech_result["alternatives"][0].get("transcript", "")
                                confidence = speech_result["alternatives"][0].get("confidence", 0.0)

                                if transcript.strip():
                                    # 发送识别结果
                                    await manager.send_personal_message(json.dumps({
                                        "type": "transcript",
                                        "transcript": transcript,
                                        "is_final": True,
                                        "confidence": confidence,
                                        "detected_language": "en-US"
                                    }), client_id)

                                    logger.info(f"API Key recognition for {client_id}: {transcript}")

                                    # 处理AI响应
                                    if gemini_model:
                                        try:
                                            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {transcript}"
                                            model_name = os.getenv("VERTEX_AI_ENDPOINT")
                                            response = gemini_model.models.generate_content(
                                                model=model_name,
                                                contents=full_prompt
                                            )

                                            ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response."

                                            await manager.send_personal_message(json.dumps({
                                                "type": "response",
                                                "response": ai_response,
                                                "timestamp": datetime.now().isoformat()
                                            }), client_id)
                                        except Exception as e:
                                            logger.error(f"Gemini processing error: {e}")

                    # 清空缓冲区并更新时间
                    audio_buffer.clear()
                    last_recognition_time = current_time

            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for client {client_id}")
                break

    except Exception as e:
        logger.error(f"API key speech handling error: {e}")
        if websocket.client_state.name == "CONNECTED":
            await manager.send_personal_message(json.dumps({
                "type": "error",
                "error": f"Speech recognition failed: {str(e)}"
            }), client_id)

# 流式处理模式的WebSocket处理函数
async def handle_speech_with_streaming(websocket: WebSocket, client_id: str, speech_client):
    """使用服务账户凭据进行流式语音识别"""
    try:
        # Configure speech recognition
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=16000,
            language_code="en-US",
            alternative_language_codes=["es-ES", "fr-FR", "de-DE", "it-IT", "pt-PT"],
            enable_automatic_punctuation=True,
            enable_word_time_offsets=False,
            model="latest_long"
        )

        streaming_config = speech.StreamingRecognitionConfig(
            config=config,
            interim_results=True,
            single_utterance=False
        )

        async def audio_stream():
            try:
                while True:
                    data = await websocket.receive_bytes()

                    if len(data) == 0:
                        logger.warning("Received empty audio data")
                        continue
                    if len(data) % 2 != 0:
                        logger.warning(f"Invalid audio data length (not even): {len(data)}")
                        continue
                    if len(data) > 1024 * 1024:
                        logger.warning(f"Audio chunk too large: {len(data)} bytes")
                        continue
                    yield data

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for client {client_id}")
                return
            except Exception as e:
                logger.error(f"Error in audio stream: {e}")
                return

        # Start streaming recognition (基于Google官方示例)
        async def request_generator():
            # 第一个请求必须只包含配置，不包含音频数据
            yield speech.StreamingRecognizeRequest(streaming_config=streaming_config)

            # 后续请求包含音频数据
            async for audio_data in audio_stream():
                if audio_data and len(audio_data) > 0:  # 确保数据不为空且有内容
                    yield speech.StreamingRecognizeRequest(audio_content=audio_data)

        # Call streaming API with generator (正确的API调用方式)
        responses = speech_client.streaming_recognize(
            config=streaming_config,
            requests=request_generator()
        )

        # Process responses in real-time
        async for response in responses:
            for result in response.results:
                transcript = result.alternatives[0].transcript
                is_final = result.is_final
                detected_language = result.language_code if hasattr(result, 'language_code') else "en-US"

                await manager.send_personal_message(json.dumps({
                    "type": "transcript",
                    "transcript": transcript,
                    "is_final": is_final,
                    "confidence": result.alternatives[0].confidence if result.alternatives else 0.0,
                    "detected_language": detected_language
                }), client_id)

                if is_final:
                    logger.info(f"Final transcript for {client_id}: {transcript}")

                    # Process transcript with Gemini
                    if gemini_model:
                        try:
                            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {transcript}"
                            model_name = os.getenv("VERTEX_AI_ENDPOINT")
                            response = gemini_model.models.generate_content(
                                model=model_name,
                                contents=full_prompt
                            )

                            ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response. Please try again."

                            await manager.send_personal_message(json.dumps({
                                "type": "response",
                                "response": ai_response,
                                "timestamp": datetime.now().isoformat()
                            }), client_id)
                        except Exception as e:
                            logger.error(f"Gemini processing error: {e}")
                            await manager.send_personal_message(json.dumps({
                                "type": "error",
                                "error": f"Chat processing error: {str(e)}"
                            }), client_id)

    except Exception as e:
        logger.error(f"Streaming recognition error: {e}")
        if websocket.client_state.name == "CONNECTED":
            await manager.send_personal_message(json.dumps({
                "type": "error",
                "error": f"Speech recognition failed: {str(e)}"
            }), client_id)

# Initialize Google Gen AI SDK
def initialize_genai():
    """Initialize Google Gen AI SDK"""
    try:
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not project_id :
            logger.error("❌ Missing required env var: GOOGLE_CLOUD_PROJECT")

        location = os.getenv("GOOGLE_CLOUD_LOCATION")
        if not location :
            logger.error("❌ Missing required env var: GOOGLE_CLOUD_LOCATION")

        model_name = os.getenv("VERTEX_AI_ENDPOINT")
        if not model_name :
            logger.error("❌ Missing required env var: VERTEX_AI_ENDPOINT")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )
        logger.info("✅ Vertex AI 初始化成功")

        return client

    except Exception as e:
        logger.error(f"❌ Failed to initialize Google Gen AI: {e}")
        return None

# Initialize the global model
gemini_model = None

# Initialize on startup
@app.on_event("startup")
async def startup_event():
    global gemini_model
    gemini_model = initialize_genai()

    if gemini_model:
        logger.info("🚀 FastAPI server started successfully with Google Gen AI SDK")
    else:
        logger.warning("❌ Failed to initialize fine-tuned Gemini model")

# Health check endpoints
@app.get("/")
async def root():
    return {
        "message": "SynTour AI API Enhanced - Running",
        "status": "healthy",
        "version": "2.0.0",
        "features": ["multimodal", "travel_planning", "file_upload", "speech_recognition"]
    }

@app.get("/health")
async def health_check():
    genai_status = "connected" if initialize_genai() else "disconnected"
    return {
        "status": "healthy",
        "service": "fastapi-backend-enhanced",
        "genai": genai_status
    }

# Test endpoint for Vertex AI Gemini
@app.post("/test-vertex")
async def test_vertex_endpoint(request: ChatRequest):
    try:
        logger.info(f"Test Vertex AI request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Create prompt
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=2048,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Test Vertex AI response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini-test",
            processing_info={"prompt_type": "test", "endpoint": "vertex"}
        )

    except Exception as e:
        logger.error(f"Test Vertex AI error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Test failed: {str(e)}",
            model="fine-tuned-gemini-test"
        )

# Simple chat endpoint (following malaysia-ai-backend pattern)
@app.post("/chat", response_model=ChatResponse)
async def simple_chat(request: ChatRequest):
    try:
        logger.info(f"Simple chat request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Simple prompt without complex templates
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Simple chat response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "simple", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Simple chat error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to generate response: {str(e)}",
            model="fine-tuned-gemini"
        )

# Enhanced AI Chat endpoint with prompt engineering
@app.post("/api/ai/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    try:
        # 验证输入
        if not request.message or not request.message.strip():
            return create_error_response(
                "Message cannot be empty",
                ErrorCode.VALIDATION_ERROR
            )

        message = request.message.strip()
        context = request.context or ""
        preferences_dict = request.user_preferences or {}
            
        # 对于纯文本聊天，不处理文件
        contents = []
        has_text = bool(message and message.strip())

        # 处理文本输入
        if has_text:
            logger.info("Processing text input")

            # Build the prompt
            full_prompt = message
            if context:
                full_prompt = f"Context: {context}\n\nUser Message: {message}"

            if preferences_dict:
                full_prompt += f"\n\nUser Preferences: {json.dumps(preferences_dict)}"
            
            contents.append(full_prompt)
        else:
            return create_error_response(
                "No message provided",
                ErrorCode.VALIDATION_ERROR
            )

        # 使用Vertex AI处理文本
        try:
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
            location = os.getenv("GOOGLE_CLOUD_LOCATION")

            client = genai.Client(
                vertexai=True,
                project=project_id,
                location=location,
            )

            model_name = os.getenv("VERTEX_AI_ENDPOINT")

            # 创建内容
            content_parts = [genai.types.Part(text=full_prompt)]

            contents = [
                genai.types.Content(
                    role="user",
                    parts=content_parts
                )
            ]

            config = genai.types.GenerateContentConfig(
                temperature=0.7,
                top_p=0.9,
                max_output_tokens=2048,
            )

            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=config
            )

            if response and response.text:
                return ChatResponse(
                    success=True,
                    response=response.text,
                    model="fine-tuned-gemini-travel"
                )
            else:
                return create_error_response(
                    "No response generated from AI model",
                    ErrorCode.PROCESSING_ERROR
                )

        except Exception as ai_error:
            logger.error(f"AI processing error: {ai_error}")
            return create_error_response(
                f"AI service error: {str(ai_error)}",
                ErrorCode.PROCESSING_ERROR
            )
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return create_error_response(
            f"Internal server error: {str(e)}",
            ErrorCode.PROCESSING_ERROR
        )


# Multimodal AI endpoint
@app.post("/api/ai/multimodal", response_model=ChatResponse)
async def multimodal_ai_chat(
    message: str = Form(...),
    context: Optional[str] = Form(None),
    files: List[UploadFile] = File(None)
):
    try:
        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Process uploaded files
        media_descriptions = {}
        media_types = []

        if files:
            for file in files:
                file_content = await file.read()
                processed = await FileProcessor.process_file(file_content, file.filename)

                if processed.get("processed"):
                    file_info = FileProcessor.get_file_info(file.filename)
                    media_types.append(file_info["type"])
                    media_descriptions[file_info["type"]] = processed.get("description", "")

        # Generate multimodal prompt
        if media_types:
            prompt = PromptTemplates.multimodal_prompt(
                user_message=message,
                media_types=media_types,
                media_descriptions=media_descriptions
            )
        else:
            # Fallback to regular travel prompt
            prompt = PromptTemplates.travel_planning_prompt(message)

        # 3. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 4. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 5. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={
                "prompt_type": "multimodal" if media_types else "text_only",
                "media_types": media_types,
                "files_processed": len(files) if files else 0
            }
        )

    except Exception as e:
        logger.error(f"Multimodal AI error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to process multimodal request: {str(e)}"
        )


# Specialized travel planning endpoint
@app.post("/api/travel/plan", response_model=ChatResponse)
async def create_travel_plan(request: ChatRequest):
    try:
        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Extract travel parameters from user preferences
        duration = None
        budget = None
        interests = None
        travelers = None

        if request.user_preferences:
            duration = request.user_preferences.get("duration")
            budget = request.user_preferences.get("budget")
            interests = request.user_preferences.get("interests")
            travelers = request.user_preferences.get("travelers")

        # Use specialized travel planning prompt
        prompt = PromptTemplates.travel_planning_prompt(
            user_message=request.message,
            duration=duration,
            budget=budget,
            interests=interests,
            travelers=travelers
        )

        # 3. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 4. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 5. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "specialized_travel_planning"}
        )

    except Exception as e:
        logger.error(f"Travel planning error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to create travel plan: {str(e)}"
        )


# Continuation endpoint for truncated responses
@app.post("/api/ai/continue/{continuation_id}", response_model=ChatResponse)
async def continue_response(continuation_id: str):
    try:
        # Check if continuation context exists
        if continuation_id not in continuation_store:
            return ChatResponse(
                success=False,
                error="Continuation context not found or expired"
            )

        context = continuation_store[continuation_id]

        # Check if context is not too old (1 hour limit)
        if time.time() - context["timestamp"] > 3600:
            del continuation_store[continuation_id]
            return ChatResponse(
                success=False,
                error="Continuation context has expired"
            )

        # Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Create continuation prompt
        continuation_prompt = f"""Please continue the following response where it was cut off:

Original question: {context['original_message']}

Previous response (incomplete):
{context['partial_response']}

Please continue from where the response was cut off, maintaining the same tone and style:"""

        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=continuation_prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        ai_response = response.text if response.text else "No continuation generated"

        # Clean up the continuation context
        del continuation_store[continuation_id]

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "continuation", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Continuation error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to continue response: {str(e)}"
        )


# 候选语言列表（可扩展）
CANDIDATE_LANGUAGES = [
    "en-US", "zh-CN", "ja-JP", "fr-FR", "es-ES",
    "de-DE", "ko-KR", "ru-RU", "it-IT", "pt-BR",
    "id-ID", "ms-MY", "ta-IN", "ta-MY", "th-TH",
    "tr-TR", "uk-UA", "uz-UZ", "vi-VN", "sv-SE"
]

@app.websocket("/ws/speech/{client_id}")
async def websocket_speech_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)

    if not speech_client:
        await websocket.send_text(json.dumps({
            "type": "error",
            "error": "Speech recognition service not available"
        }))
        await websocket.close()
        return

    # 检查是否使用API密钥模式
    use_api_key = speech_client == "api_key_mode"

    try:
        if use_api_key:
            # API密钥模式：收集音频数据并批量处理
            await handle_speech_with_api_key(websocket, client_id)
        else:
            # 服务账户模式：使用流式处理
            await handle_speech_with_streaming(websocket, client_id, speech_client)

    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket speech endpoint error: {e}")
        if websocket.client_state.name == "CONNECTED":
            await manager.send_personal_message(json.dumps({
                "type": "error",
                "error": f"Speech recognition failed: {str(e)}"
            }), client_id)
    finally:
        try:
            # 检查WebSocket状态并安全关闭
            if websocket.client_state.name not in ["DISCONNECTED", "CLOSED"]:
                await websocket.close(code=1000, reason="Normal closure")
                logger.info(f"WebSocket closed for client {client_id}")
        except Exception as e:
            logger.error(f"Error closing WebSocket for client {client_id}: {e}")
        finally:
            # 确保连接从管理器中移除
            manager.disconnect(client_id)

@app.websocket("/ws/chat/{client_id}")
async def websocket_chat_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()

            # 添加JSON验证
            try:
                message_data = json.loads(data)
                if not isinstance(message_data, dict) or "message" not in message_data:
                    raise ValueError("Invalid message format")
            except (json.JSONDecodeError, ValueError) as e:
                error_msg = create_websocket_error_message(
                    f"Invalid message format: {str(e)}",
                    ErrorCode.VALIDATION_ERROR,
                    client_id
                )
                await manager.send_personal_message(error_msg, client_id)
                continue
            
            if not gemini_model:
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "error": "Chat service not available"
                }), client_id)
                continue
            
            # Process the message
            user_message = message_data.get("message", "")
            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {user_message}"
            
            try:
                model_name = os.getenv("VERTEX_AI_ENDPOINT")
                response = gemini_model.models.generate_content(
                    model=model_name,
                    contents=full_prompt
                )
                
                ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response. Please try again."

                await manager.send_personal_message(json.dumps({
                    "type": "response",
                    "response": ai_response.text,
                    "timestamp": datetime.now().isoformat()
                }), client_id)
                
            except Exception as e:
                logger.error(f"Chat generation error: {e}")
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "error": f"Failed to generate response: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }), client_id)
    
    except WebSocketDisconnect:
        logger.info(f"Chat client {client_id} disconnected")
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Chat WebSocket error for client {client_id}: {e}")
    finally:
        try:
            # 检查WebSocket状态并安全关闭
            if websocket.client_state.name not in ["DISCONNECTED", "CLOSED"]:
                await websocket.close(code=1000, reason="Normal closure")
                logger.info(f"Chat WebSocket closed for client {client_id}")
        except Exception as e:
            logger.error(f"Error closing chat WebSocket for client {client_id}: {e}")
        finally:
            # 确保连接从管理器中移除
            manager.disconnect(client_id)




if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_enhanced:app", host="0.0.0.0", port=8002, reload=True)
