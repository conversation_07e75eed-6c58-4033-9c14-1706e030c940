@tailwind base;
@tailwind components;
@tailwind utilities;

:root{
  /* 核心马来西亚色彩变量 */
  --my-primary:#010066;
  --my-secondary:#FFCC00;
  --my-accent:#CC0000;
  --my-background:#FAF9F6;
  --my-ink:#212529;
  --my-success:#28A745;
  --my-warning:#FFC107;
  
  /* 扩展色彩变量 */
  --my-blue-50:#F0F4FF;
  --my-blue-100:#E1E9FF;
  --my-blue-200:#C3D3FF;
  --my-blue-300:#A5BDFF;
  --my-blue-400:#87A7FF;
  --my-blue-500:#6991FF;
  --my-blue-600:#4B7BFF;
  --my-blue-700:#2D65FF;
  --my-blue-800:#0F4FFF;
  --my-blue-900:#010066;
  
  --my-gold-50:#FFFDF0;
  --my-gold-100:#FFFBE1;
  --my-gold-200:#FFF7C3;
  --my-gold-300:#FFF3A5;
  --my-gold-400:#FFEF87;
  --my-gold-500:#FFEB69;
  --my-gold-600:#FFE74B;
  --my-gold-700:#FFE32D;
  --my-gold-800:#FFDF0F;
  --my-gold-900:#FFCC00;
  
  --my-red-50:#FFF0F0;
  --my-red-100:#FFE1E1;
  --my-red-200:#FFC3C3;
  --my-red-300:#FFA5A5;
  --my-red-400:#FF8787;
  --my-red-500:#FF6969;
  --my-red-600:#FF4B4B;
  --my-red-700:#FF2D2D;
  --my-red-800:#FF0F0F;
  --my-red-900:#CC0000;
  
  --my-green-50:#F0FFF0;
  --my-green-100:#E1FFE1;
  --my-green-200:#C3FFC3;
  --my-green-300:#A5FFA5;
  --my-green-400:#87FF87;
  --my-green-500:#69FF69;
  --my-green-600:#4BFF4B;
  --my-green-700:#2DFF2D;
  --my-green-800:#0FFF0F;
  --my-green-900:#28A745;
  
  /* 马来西亚文化特色 */
  --ls-display: -0.02em;
  --malaysia-shadow: 0 8px 32px rgba(1, 0, 102, 0.15);
  --gold-shadow: 0 4px 20px rgba(255, 204, 0, 0.3);
}

html,body{ height:100%; }
body{
  color: var(--my-ink);
  /* 马来西亚热带背景 - 沙滩到海洋的渐变 */
  background: 
    radial-gradient(1200px 800px at 20% 20%, var(--my-gold-50) 0%, var(--my-gold-100) 30%, var(--my-blue-50) 70%, var(--my-blue-100) 100%),
    linear-gradient(135deg, rgba(1,0,102,.03), rgba(255,204,0,.03), rgba(40,167,69,.02));
}

/* Typography defaults */
body{ font-family: theme(fontFamily.body); }
h1,h2,h3,h4,h5,h6{ 
  font-family: theme(fontFamily.display); 
  font-weight: 700;
  font-size: 2.5rem;
  letter-spacing: var(--ls-display, -0.02em); 
}

.container-page{ @apply mx-auto max-w-6xl px-4; }

/* 马来西亚风格卡片 */
.card{ 
  @apply rounded-2xl bg-white shadow-malaysia border border-white/70; 
  border-radius: 1rem;
  box-shadow: var(--malaysia-shadow);
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(240,244,255,0.9));
  backdrop-filter: blur(10px);
}

/* 马来西亚风格按钮 */
.btn{ 
  @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium transition-all duration-300; 
  border-radius: 1.2rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--my-primary), var(--my-blue-600), var(--my-secondary));
  color: white;
  box-shadow: var(--malaysia-shadow);
}

.btn:hover{ 
  transform: translateY(-2px);
  box-shadow: var(--gold-shadow);
  background: linear-gradient(135deg, var(--my-blue-600), var(--my-secondary), var(--my-red-500));
}

.btn-primary{
  color:#fff;
  background: linear-gradient(135deg, var(--my-primary), var(--my-blue-600), var(--my-secondary));
  box-shadow: var(--malaysia-shadow);
}

.btn-primary:hover{ 
  transform: translateY(-2px);
  box-shadow: var(--gold-shadow);
  background: linear-gradient(135deg, var(--my-blue-600), var(--my-secondary), var(--my-red-500));
}

.btn-primary:focus-visible{
  outline: 3px solid var(--my-secondary);
  outline-offset: 2px;
}

.btn-secondary{ 
  @apply border border-gray-200 bg-white; 
  border-radius: 1.2rem;
  font-weight: 600;
  border-color: var(--my-blue-200);
  color: var(--my-primary);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--gold-shadow);
  background: linear-gradient(135deg, var(--my-blue-600), var(--my-secondary), var(--my-red-500));
  color: white;
  border-color: transparent;
}

.btn-danger{ 
  background: linear-gradient(135deg, var(--my-red-600), var(--my-red-500)); 
  color:#fff;
  box-shadow: 0 4px 20px rgba(204, 0, 0, 0.3);
}

.btn-success{ 
  background: linear-gradient(135deg, var(--my-green-600), var(--my-green-500)); 
  color:#fff;
  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
}

/* 马来西亚风格输入框 */
.input{
  @apply w-full rounded-xl border border-gray-200 bg-white/90 px-3 py-2.5 outline-none transition-all duration-300;
  border-color: var(--my-blue-200);
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(240,244,255,0.9));
}

.input:focus{
  box-shadow: 0 0 0 3px rgba(1, 0, 102, 0.15);
  border-color: var(--my-blue-400);
  background: white;
  transform: translateY(-1px);
}

/* 马来西亚风格链接 */
.link{
  color: var(--my-primary);
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 2px;
  text-decoration-color: var(--my-secondary);
  transition: all 0.3s ease;
}

.link:hover{ 
  color: var(--my-blue-600);
  text-decoration-color: var(--my-red-500);
}

/* 马来西亚风格徽章 */
.badge-gold{ 
  background: linear-gradient(135deg, var(--my-gold-200), var(--my-gold-300));
  color: var(--my-ink);
  box-shadow: var(--gold-shadow);
}

.badge-red{ 
  background: linear-gradient(135deg, var(--my-red-200), var(--my-red-300));
  color: white;
  box-shadow: 0 4px 20px rgba(204, 0, 0, 0.2);
}

/* 马来西亚风格瓦片 */
.tile { 
  @apply rounded-2xl bg-white shadow-malaysia border; 
  border-radius: 1rem;
  box-shadow: var(--malaysia-shadow);
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(240,244,255,0.9));
  backdrop-filter: blur(10px);
  border-color: rgba(1, 0, 102, 0.1);
}

.tile:hover {
  transform: translateY(-2px);
  box-shadow: var(--gold-shadow);
  border-color: var(--my-secondary);
}

/* 马来西亚风格头部边框 */
.header-border {
  border-bottom: 3px solid;
  border-image: linear-gradient(90deg, var(--my-primary), var(--my-secondary), var(--my-red-500)) 1;
  background: linear-gradient(90deg, var(--my-blue-50), var(--my-gold-50), var(--my-red-50));
  background-size: 200% 100%;
  animation: malaysia-shimmer 3s ease-in-out infinite;
}

@keyframes malaysia-shimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 马来西亚风格页脚 */
.footer {
  background: linear-gradient(135deg, var(--my-primary), var(--my-blue-700), var(--my-blue-600));
  color: #ffffff;
  border-top: 3px solid;
  border-image: linear-gradient(90deg, var(--my-secondary), var(--my-red-500), var(--my-green-500)) 1;
  box-shadow: var(--malaysia-shadow);
}

/* 马来西亚风格特殊效果 */
.malaysia-glow {
  box-shadow: 
    0 0 20px rgba(1, 0, 102, 0.2),
    0 0 40px rgba(255, 204, 0, 0.1),
    0 0 60px rgba(204, 0, 0, 0.05);
}

.tropical-border {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, var(--my-primary), var(--my-secondary), var(--my-red-500), var(--my-green-500)) border-box;
}

/* 马来西亚文化图案背景 */
.batik-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, var(--my-gold-200) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--my-blue-200) 2px, transparent 2px),
    radial-gradient(circle at 50% 50%, var(--my-red-200) 1px, transparent 1px);
  background-size: 20px 20px, 30px 30px, 15px 15px;
  opacity: 0.1;
}

/* AI Assistant 呼吸动画 */
@keyframes breathing {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(66, 153, 225, 0);
  }
}

.ai-breathing {
  animation: breathing 3s ease-in-out infinite;
}

/* 隐藏滚动条但保持功能 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
