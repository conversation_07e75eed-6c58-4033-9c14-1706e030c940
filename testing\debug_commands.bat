@echo off
echo ========================================
echo SynTour Debug Commands
echo ========================================

echo.
echo 1. Checking if backend is running on port 8002...
netstat -an | findstr :8002
if %errorlevel% neq 0 (
    echo ❌ No service found on port 8002
) else (
    echo ✅ Service found on port 8002
)

echo.
echo 2. Testing backend health endpoint...
curl -s http://localhost:8002/ 2>nul
if %errorlevel% neq 0 (
    echo ❌ Backend health check failed
) else (
    echo ✅ Backend responded
)

echo.
echo 3. Testing WebSocket connection (basic)...
echo This will attempt a basic WebSocket handshake:
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://localhost:8002/ws/speech 2>nul
if %errorlevel% neq 0 (
    echo ❌ WebSocket handshake failed
) else (
    echo ✅ WebSocket handshake successful
)

echo.
echo 4. Checking Google Cloud credentials...
if exist "C:\Users\<USER>\Desktop\TRAVEL_AI\silver-course-470107-h0-de21f66e46dc.json" (
    echo ✅ Google Cloud credentials file found
) else (
    echo ❌ Google Cloud credentials file NOT found
    echo Expected location: C:\Users\<USER>\Desktop\TRAVEL_AI\silver-course-470107-h0-de21f66e46dc.json
)

echo.
echo 5. Checking Python dependencies...
python -c "import google.cloud.speech; print('✅ Google Cloud Speech library available')" 2>nul || echo "❌ Google Cloud Speech library not available"
python -c "import fastapi; print('✅ FastAPI available')" 2>nul || echo "❌ FastAPI not available"
python -c "import websockets; print('✅ WebSockets library available')" 2>nul || echo "❌ WebSockets library not available"

echo.
echo ========================================
echo Debug completed
echo ========================================
pause