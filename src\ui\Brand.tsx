"use client";
import Image from "next/image";

export default function Brand() {
  return (
    <div className="flex items-center gap-3">
      {/* 进一步缩小的图标容器 - 确保SynTour文字完全可见 */}
      <div className="relative w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 flex items-center justify-center">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src="/branding/syntour.png"
          onError={(e) => { 
            const target = e.currentTarget as HTMLImageElement;
            if (target.src !== "/branding/syntour-placeholder.svg") {
              target.src = "/branding/syntour-placeholder.svg";
            }
          }}
          alt="SynTour"
          className="w-full h-full object-contain"
        />
      </div>
      
      {/* 文字在框架内居中 - 确保SynTour完全可见 */}
      <div className="flex items-center">
        <span className="font-extrabold tracking-tight text-my-primary text-sm md:text-base lg:text-lg">
          SynTour
        </span>
      </div>
    </div>
  );
}
