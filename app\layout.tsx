import "./globals.css";
import { Plus_Jakarta_Sans, Inter } from "next/font/google";
import ReduxProvider from "@/providers/ReduxProvider";
import Header from "@/ui/Header";

const display = Plus_Jakarta_Sans({
  subsets: ["latin"],
  weight: ["600","700","800"],
  variable: "--font-display",
  display: "swap",
});
const body = Inter({
  subsets: ["latin"],
  weight: ["400","500","600"],
  variable: "--font-body",
  display: "swap",
});

export const metadata = { title: "SynTour", description: "Malaysia travel planner powered by AI" };

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.png" type="image/png" />
      </head>
      <body className={`${display.variable} ${body.variable} min-h-screen`}>
        <ReduxProvider>
          <Header />
          <main className="min-h-screen">
            {children}
          </main>
        </ReduxProvider>
      </body>
    </html>
  );
}
