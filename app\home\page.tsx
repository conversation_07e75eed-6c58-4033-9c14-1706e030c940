import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { Plane, Hotel, Sandwich, MapPin, Landmark, HelpCircle, TreePine, Building2, Utensils } from "lucide-react";
import Brand from "@/ui/Brand";
import ChatContainer from "@/ui/ChatContainer";
import SupportDock from "@/ui/SupportDock";
import SupportWidget from "@/ui/SupportWidget";

export default function Home(){
  const token = cookies().get("demo_token")?.value;
  if(!token) redirect("/auth/login");

  const cards = [
    { 
      title: "Build Itinerary", 
      desc: "Tailored for you", 
      subtitle: "AI-powered Malaysia itineraries",
      href: "/itinerary", 
      icon: Landmark, 
      gradient: "from-my-blue-50 to-my-gold-50",
      hoverGradient: "from-blue-400 via-yellow-400 to-blue-500",
      malaysianIcon: "🏛️",
      color: "text-my-blue-600"
    },
    { 
      title: "Find Flights", 
      desc: "Smart deals from multiple sources", 
      subtitle: "Direct flights to KL & Penang",
      href: "/flights", 
      icon: Plane, 
      gradient: "from-my-blue-100 to-my-blue-200",
      hoverGradient: "from-blue-400 via-yellow-400 to-blue-500",
      malaysianIcon: "✈️",
      color: "text-my-blue-700"
    },
    { 
      title: "Find Hotels", 
      desc: "Perfect match to your needs", 
      subtitle: "From KLCC towers to beach resorts",
      href: "/hotels", 
      icon: Hotel, 
      gradient: "from-my-gold-50 to-my-green-50",
      hoverGradient: "from-yellow-400 via-blue-400 to-green-500",
      malaysianIcon: "🏨",
      color: "text-my-gold-600"
    },
    { 
      title: "Find Foods", 
      desc: "Best local eats and cafes", 
      subtitle: "Discover Malaysia's best Nasi Lemak",
      href: "/food", 
      icon: Utensils, 
      gradient: "from-my-red-50 to-my-gold-100",
      hoverGradient: "from-red-400 via-yellow-400 to-blue-500",
      malaysianIcon: "🍜",
      color: "text-my-red-600"
    },
    { 
      title: "Attractions", 
      desc: "Top sights & hidden gems", 
      subtitle: "From Petronas Towers to Batu Caves",
      href: "/attractions", 
      icon: Building2, 
      gradient: "from-my-green-50 to-my-blue-100",
      hoverGradient: "from-green-400 via-blue-400 to-yellow-500",
      malaysianIcon: "🏙️",
      color: "text-my-green-600"
    },
    { 
      title: "No idea?", 
      desc: "Let SynTour suggest step by step", 
      subtitle: "Perfect for first-time visitors",
      href: "/wizard", 
      icon: TreePine, 
      gradient: "from-my-neutral-50 to-my-blue-50",
      hoverGradient: "from-blue-400 via-green-400 to-yellow-500",
      malaysianIcon: "🌴",
      color: "text-my-primary"
    }
  ];

  return (
    <div className="min-h-screen">
      <div className="container-page py-10 flex flex-col items-center">
        <div className="w-full max-w-6xl text-center">
            
            {/* 马来西亚风格主标题 - 修复布局避免重叠 */}
            <div className="flex flex-col items-center justify-center mt-2 mb-4">
              <div className="flex items-center justify-center gap-4">
                <h1 className="font-extrabold text-my-ink tracking-tight md:tracking-tighter2 lg:tracking-tighter3 leading-tight text-2xl md:text-3xl lg:text-4xl malaysia-glow">
                  Begin Your Next Adventure
                </h1>
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 flex items-center justify-center">
                    <span className="text-2xl md:text-3xl lg:text-4xl">✈️</span>
                  </div>
                </div>
              </div>
            </div>
            
            <p className="mt-1 max-w-2xl mx-auto text-[13px] md:text-[14px] text-gray-600">
              I&apos;m your AI trip partner for Malaysia and beyond. Tell me dates, budget and vibe—SynTour will craft a plan you&apos;ll love.
            </p>

            {/* 马来西亚风格卡片网格 - 增强交互效果 */}
            <div className="grid md:grid-cols-3 gap-4 mt-6 max-w-4xl mx-auto">
              {cards.map((c,i)=>(
                <div 
                  key={i} 
                  className={`group rounded-xl p-5 bg-gradient-to-br ${c.gradient} shadow-sm border text-center transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:scale-105 cursor-pointer relative overflow-hidden`}
                >
                  {/* Hover动态渐变背景 */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${c.hoverGradient} opacity-0 group-hover:opacity-90 transition-opacity duration-300 rounded-xl`}></div>
                  
                  {/* 卡片内容 */}
                  <div className="relative z-10">
                    <div className="flex flex-col items-center gap-3">
                      <div className={`text-2xl md:text-3xl ${c.color} group-hover:text-white transition-colors duration-300`}>
                        {c.malaysianIcon}
                      </div>
                      <div className="font-semibold text-[15px] md:text-[16px] text-my-ink group-hover:text-white transition-colors duration-300">
                        {c.title}
                      </div>
                    </div>
                    <div className="text-[12px] md:text-[13px] text-gray-600 group-hover:text-white/90 mt-2 transition-colors duration-300">
                      {c.desc}
                    </div>
                    
                    {/* 新增副标题 */}
                    <div className="text-[10px] md:text-[11px] text-gray-500 group-hover:text-white/80 mt-2 font-medium transition-colors duration-300 border-t border-gray-200/50 group-hover:border-white/30 pt-2">
                      {c.subtitle}
                    </div>
                  </div>
                </div>
              ))}
            </div>
        </div>

        {/* Seamless Chat Integration */}
        <div className="w-full mt-16">
          <ChatContainer />
        </div>

        {/* Bottom Widgets */}
        <div className="w-full mt-12 space-y-6">
          <SupportDock />
          <SupportWidget />
        </div>
      </div>
    </div>
  );
}
