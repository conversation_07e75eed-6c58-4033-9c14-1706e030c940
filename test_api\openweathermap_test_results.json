{"summary": {"total": 28, "passed": 28, "failed": 0, "success_rate": 100.0}, "results": [{"test": "Current Weather - London", "success": true, "timestamp": "2025-08-30T13:31:05.197882", "details": {"city": "London", "country": "GB", "temperature": 12.6, "condition": "Clouds", "units": "metric"}}, {"test": "Weather Response Structure", "success": true, "timestamp": "2025-08-30T13:31:05.198789", "details": {"missing_fields": [], "available_fields": ["coord", "weather", "base", "main", "visibility", "wind", "clouds", "dt", "sys", "timezone"]}}, {"test": "Current Weather - Imperial Units", "success": true, "timestamp": "2025-08-30T13:31:05.399421", "details": {"city": "New York", "temperature_f": 61.99, "units": "imperial"}}, {"test": "Current Weather - Spanish Language", "success": true, "timestamp": "2025-08-30T13:31:05.631634", "details": {"city": "Madrid", "description": "cielo claro", "language": "es"}}, {"test": "Current Weather - Coordinates (Paris)", "success": true, "timestamp": "2025-08-30T13:31:05.901392", "details": {"city": "Saint<PERSON><PERSON><PERSON>", "latitude": 48.8566, "longitude": 2.3522, "temperature": 13.6}}, {"test": "Coordinate Accuracy", "success": true, "timestamp": "2025-08-30T13:31:05.902054", "details": {"requested_lat": 48.8566, "returned_lat": 48.8566, "requested_lon": 2.3522, "returned_lon": 2.3522, "lat_diff": 0.0, "lon_diff": 0.0}}, {"test": "Current Weather - Tokyo Coordinates", "success": true, "timestamp": "2025-08-30T13:31:06.146983", "details": {"city": "<PERSON><PERSON><PERSON>", "timezone": 32400}}, {"test": "Current Weather - US ZIP Code", "success": true, "timestamp": "2025-08-30T13:31:06.375736", "details": {"city": "New York", "country": "US", "zip_area": "10001, US", "temperature": 16.64}}, {"test": "Current Weather - UK Postal Code", "success": true, "timestamp": "2025-08-30T13:31:06.566274", "details": {"city": "SW1A 1AA", "postal_code": "SW1A 1AA, GB"}}, {"test": "Geocoding - City to Coordinates", "success": true, "timestamp": "2025-08-30T13:31:06.648100", "details": {"city": "Berlin", "country": "DE", "latitude": 52.5170365, "longitude": 13.3888599}}, {"test": "Reverse Geocoding - Coordinates to City", "success": true, "timestamp": "2025-08-30T13:31:06.775700", "details": {"original_city": "Berlin", "reverse_city": "Berlin", "country": "DE"}}, {"test": "Weather Forecast - 5 Day", "success": true, "timestamp": "2025-08-30T13:31:06.853650", "details": {"city": "London", "country": "GB", "forecast_entries": 10, "requested_count": 10}}, {"test": "Forecast Entry Structure", "success": true, "timestamp": "2025-08-30T13:31:06.854638", "details": {"missing_fields": [], "forecast_time": "2025-08-30 06:00:00", "temperature": 12.81}}, {"test": "Weather Forecast - By Coordinates", "success": true, "timestamp": "2025-08-30T13:31:07.023197", "details": {"location": "New York (coordinates)", "units": "imperial", "entries": 5}}, {"test": "Air Pollution - Current Data", "success": true, "timestamp": "2025-08-30T13:31:07.325359", "details": {"location": "London", "aqi": 2, "co": 113.39, "pm2_5": 0.68, "pm10": 1.65}}, {"test": "Air Pollution - Components", "success": true, "timestamp": "2025-08-30T13:31:07.326358", "details": {"available_components": ["co", "no", "no2", "o3", "so2", "pm2_5", "pm10", "nh3"], "total_available": 8}}, {"test": "UV Index - Current Data", "success": true, "timestamp": "2025-08-30T13:31:07.460756", "details": {"location": "Sydney", "uv_index": 3.84, "date": "2025-08-30T12:00:00Z", "coordinates": "-33.8688, 151.2093"}}, {"test": "UV Index - Valid Range", "success": true, "timestamp": "2025-08-30T13:31:07.461659", "details": {"uv_value": 3.84, "valid_range": "0-15"}}, {"test": "Temperature Conversions", "success": true, "timestamp": "2025-08-30T13:31:07.463198", "details": {"kelvin": 293.15, "celsius": 20.0, "fahrenheit": 68.0}}, {"test": "Weather Icon URL", "success": true, "timestamp": "2025-08-30T13:31:07.464261", "details": {"generated_url": "https://openweathermap.org/img/wn/<EMAIL>", "expected_url": "https://openweathermap.org/img/wn/<EMAIL>"}}, {"test": "Weather Severity Levels", "success": true, "timestamp": "2025-08-30T13:31:07.464261", "details": {"test_cases": 6, "passed": 6, "severity_tests": {"800": "none", "801": "light", "500": "light", "502": "moderate", "200": "severe", "781": "severe"}}}, {"test": "Travel Recommendations - Structure", "success": true, "timestamp": "2025-08-30T13:31:07.581902", "details": {"missing_keys": [], "recommendation_score": 8, "activities_count": 3, "warnings_count": 0}}, {"test": "Travel Recommendations - Score Range", "success": true, "timestamp": "2025-08-30T13:31:07.582923", "details": {"score": 8, "valid_range": "0-10", "recommendation_text": "Excellent weather for travel and outdoor activities!"}}, {"test": "Weather Comparison - Multiple Cities", "success": true, "timestamp": "2025-08-30T13:31:07.921086", "details": {"requested_locations": 3, "successful_locations": 3, "warmest_location": "Berlin", "coldest_location": "London"}}, {"test": "Weather Comparison - Analysis", "success": true, "timestamp": "2025-08-30T13:31:07.921086", "details": {"available_analysis": ["warmest_location", "coldest_location", "most_humid_location", "windiest_location"], "analysis_completeness": "4/4"}}, {"test": "Erro<PERSON> - Invalid City", "success": true, "timestamp": "2025-08-30T13:31:08.160960", "details": {"expected_error": ""}}, {"test": "Error Handling - Invalid Coordinates", "success": true, "timestamp": "2025-08-30T13:31:08.263644", "details": {"expected_error": ""}}, {"test": "<PERSON><PERSON><PERSON> - Invalid <PERSON>", "success": true, "timestamp": "2025-08-30T13:31:08.354427", "details": {"expected_error": ""}}]}