#!/usr/bin/env python3
"""
修复验证脚本 - 基于Google Cloud官方文档的修复验证
验证语音识别API、FileProcessor和WebSocket连接的修复
"""

import os
import sys
import importlib.util

def check_google_cloud_setup():
    """检查Google Cloud配置"""
    print("🔍 检查Google Cloud配置...")
    
    # 检查环境变量
    required_vars = [
        "GOOGLE_APPLICATION_CREDENTIALS",
        "GOOGLE_CLOUD_PROJECT", 
        "VERTEX_AI_ENDPOINT"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    # 检查凭据文件
    creds_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if not os.path.exists(creds_path):
        print(f"❌ 凭据文件不存在: {creds_path}")
        return False
    
    print("✅ Google Cloud配置检查通过")
    return True

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        "google.cloud.speech",
        "google.genai", 
        "fastapi",
        "websockets",
        "pydantic"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        return False
    
    print("✅ 依赖包检查通过")
    return True

def check_file_structure():
    """检查文件结构"""
    print("🔍 检查文件结构...")
    
    required_files = [
        "backend/main_enhanced.py",
        "backend/services/file_processor.py",
        "backend/services/prompt_templates.py",
        "src/services/aiService.ts",
        "src/ui/VoiceRecorder.tsx"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件结构检查通过")
    return True

def check_code_syntax():
    """检查代码语法"""
    print("🔍 检查Python代码语法...")
    
    python_files = [
        "backend/main_enhanced.py",
        "backend/services/file_processor.py"
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, file_path, 'exec')
        except SyntaxError as e:
            print(f"❌ 语法错误在 {file_path}: {e}")
            return False
        except Exception as e:
            print(f"❌ 检查 {file_path} 时出错: {e}")
            return False
    
    print("✅ Python代码语法检查通过")
    return True

def check_api_fixes():
    """检查API修复"""
    print("🔍 检查API修复...")
    
    try:
        # 检查main_enhanced.py中的修复
        with open("backend/main_enhanced.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查语音识别API修复
        if ("streaming_recognize(" in content and
            "config=streaming_config" in content and
            "requests=request_generator()" in content):
            print("✅ 语音识别API调用已修复")
        else:
            print("❌ 语音识别API调用未修复")
            return False
        
        # 检查FileProcessor重复定义是否已删除
        if content.count("class FileProcessor:") <= 1:
            print("✅ FileProcessor重复定义已清理")
        else:
            print("❌ FileProcessor仍有重复定义")
            return False
        
        # 检查异常处理顺序
        if "except WebSocketDisconnect:" in content and "except Exception as e:" in content:
            websocket_pos = content.find("except WebSocketDisconnect:")
            exception_pos = content.find("except Exception as e:")
            if websocket_pos < exception_pos:
                print("✅ 异常处理顺序已修正")
            else:
                print("❌ 异常处理顺序需要修正")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查API修复时出错: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始修复验证...")
    print("基于Google Cloud Speech API官方文档的修复验证")
    print("=" * 60)
    
    checks = [
        ("Google Cloud配置", check_google_cloud_setup),
        ("依赖包", check_dependencies),
        ("文件结构", check_file_structure),
        ("代码语法", check_code_syntax),
        ("API修复", check_api_fixes)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
        print()
    
    # 输出结果
    print("=" * 60)
    print("📊 验证结果:")
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有验证通过！修复已完成。")
        print("\n📋 修复总结:")
        print("  ✅ 语音识别API调用已修复 (基于Google官方文档)")
        print("  ✅ FileProcessor类冲突已解决")
        print("  ✅ WebSocket错误处理已改进")
        print("  ✅ 聊天API请求格式已修正")
        print("  ✅ 异常处理顺序已优化")
        print("\n🚀 现在可以启动服务进行测试:")
        print("  1. cd backend && python main_enhanced.py")
        print("  2. npm run dev")
        print("  3. python quick_test.py")
    else:
        print("⚠️ 部分验证失败，请检查相关问题。")
        print("\n📋 可能的解决方案:")
        print("  1. 确保所有环境变量正确设置")
        print("  2. 安装缺少的依赖包")
        print("  3. 检查文件路径和权限")
        print("  4. 验证Google Cloud凭据")
    
    return all_passed

if __name__ == "__main__":
    print("修复验证脚本 - 基于Google Cloud官方文档")
    result = main()
    sys.exit(0 if result else 1)
