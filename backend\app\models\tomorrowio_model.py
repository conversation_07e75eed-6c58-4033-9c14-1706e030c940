# app/models/tomorrowio_model.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from enum import Enum

class TomorrowIOError(Exception):
    """Custom exception for Tomorrow.io API errors"""
    def __init__(self, message: str, status_code: int = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class Units(str, Enum):
    """Units for weather data"""
    METRIC = "metric"
    IMPERIAL = "imperial"

class WeatherFields(str, Enum):
    """Available weather fields for realtime weather"""
    # Temperature fields
    TEMPERATURE = "temperature"
    TEMPERATURE_APPARENT = "temperatureApparent"
    
    # Humidity and precipitation
    HUMIDITY = "humidity"
    PRECIPITATION_INTENSITY = "precipitationIntensity"
    PRECIPITATION_PROBABILITY = "precipitationProbability"
    
    # Wind
    WIND_SPEED = "windSpeed"
    WIND_DIRECTION = "windDirection"
    
    # Weather conditions
    WEATHER_CODE = "weatherCode"
    CLOUD_COVER = "cloudCover"
    VISIBILITY = "visibility"
    UV_INDEX = "uvIndex"

# Weather data models
class WeatherValues(BaseModel):
    """Weather values from Tomorrow.io API"""
    temperature: Optional[float] = Field(None, description="Temperature")
    temperatureApparent: Optional[float] = Field(None, description="Apparent temperature")
    humidity: Optional[float] = Field(None, description="Humidity percentage")
    precipitationIntensity: Optional[float] = Field(None, description="Precipitation intensity")
    precipitationProbability: Optional[float] = Field(None, description="Precipitation probability")
    windSpeed: Optional[float] = Field(None, description="Wind speed")
    windDirection: Optional[float] = Field(None, description="Wind direction")
    weatherCode: Optional[int] = Field(None, description="Weather code")
    cloudCover: Optional[float] = Field(None, description="Cloud cover percentage")
    visibility: Optional[float] = Field(None, description="Visibility")
    uvIndex: Optional[float] = Field(None, description="UV index")

class WeatherLocation(BaseModel):
    """Location information from Tomorrow.io API"""
    lat: Optional[float] = Field(None, description="Latitude")
    lon: Optional[float] = Field(None, description="Longitude")
    name: Optional[str] = Field(None, description="Location name")
    type: Optional[str] = Field(None, description="Location type")

class WeatherData(BaseModel):
    """Weather data container"""
    time: Optional[str] = Field(None, description="Data timestamp")
    values: WeatherValues = Field(..., description="Weather values")

# API Response models
class RealtimeWeatherResponse(BaseModel):
    """Response model for realtime weather"""
    data: WeatherData = Field(..., description="Weather data")
    location: Optional[WeatherLocation] = Field(None, description="Location information")

# Weather code mappings
WEATHER_CODE_DESCRIPTIONS = {
    0: "Unknown",
    1000: "Clear, Sunny",
    1100: "Mostly Clear",
    1101: "Partly Cloudy",
    1102: "Mostly Cloudy",
    1001: "Cloudy",
    2000: "Fog",
    2100: "Light Fog",
    4000: "Drizzle",
    4001: "Rain",
    4200: "Light Rain",
    4201: "Heavy Rain",
    5000: "Snow",
    5001: "Flurries",
    5100: "Light Snow",
    5101: "Heavy Snow",
    6000: "Freezing Drizzle",
    6001: "Freezing Rain",
    6200: "Light Freezing Rain",
    6201: "Heavy Freezing Rain",
    7000: "Ice Pellets",
    7101: "Heavy Ice Pellets",
    7102: "Light Ice Pellets",
    8000: "Thunderstorm",
}

# Utility functions
def get_weather_description(weather_code: int) -> str:
    """Get weather description from weather code"""
    return WEATHER_CODE_DESCRIPTIONS.get(weather_code, "Unknown")

def calculate_comfort_score(temperature: float, humidity: float, wind_speed: float) -> float:
    """Calculate weather comfort score (0-10)"""
    # Base comfort temperature range (20-25°C)
    temp_score = 10
    if temperature < 15 or temperature > 30:
        temp_score = max(0, 10 - abs(temperature - 22.5) * 0.5)
    elif temperature < 18 or temperature > 27:
        temp_score = max(5, 10 - abs(temperature - 22.5) * 0.3)
    
    # Humidity comfort (40-60%)
    humidity_score = 10
    if humidity < 30 or humidity > 70:
        humidity_score = max(0, 10 - abs(humidity - 50) * 0.2)
    elif humidity < 35 or humidity > 65:
        humidity_score = max(5, 10 - abs(humidity - 50) * 0.1)
    
    # Wind comfort (0-15 km/h)
    wind_score = 10
    if wind_speed > 25:
        wind_score = max(0, 10 - (wind_speed - 15) * 0.3)
    elif wind_speed > 20:
        wind_score = max(5, 10 - (wind_speed - 15) * 0.2)
    
    # Weighted average
    comfort_score = (temp_score * 0.5 + humidity_score * 0.3 + wind_score * 0.2)
    return round(comfort_score, 1)