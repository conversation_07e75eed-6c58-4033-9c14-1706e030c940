# Dependencies
node_modules/
/.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Vercel
.vercel

# Turbo
.turbo

# Sentry Config File
.sentryclirc

# ==============================================
# PYTHON BACKEND SPECIFIC
# ==============================================

# Python Virtual Environments
backend/venv/
backend/env/
backend/.env/
backend/.venv/
**/venv/
**/env/
**/.env/
**/.venv/
*.virtualenv

# Python cache
backend/__pycache__/
backend/**/__pycache__/
backend/*.py[cod]
backend/*$py.class
backend/*.so
backend/.Python
backend/.pytest_cache/
backend/.coverage
backend/htmlcov/

# Python distribution / packaging
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/*.egg-info/
backend/.installed.cfg
backend/*.egg
backend/MANIFEST

# Python environments
backend/.env
backend/.venv
backend/env/
backend/venv/
backend/ENV/
backend/env.bak/
backend/venv.bak/

# Google Cloud credentials (SECURITY!)
backend/config/service-account-key.json
backend/**/*service-account*.json
backend/**/*credentials*.json
**/*service-account*.json
**/*credentials*.json

# AI/ML model files (large files)
backend/models/
backend/*.model
backend/*.pkl
backend/*.joblib
backend/*.h5
backend/*.pb

# ==============================================
# CLAUDE/CURSOR AI SPECIFIC
# ==============================================

# Cursor IDE files
.cursor/
**/.cursor/
.cursorrules
**/.cursorrules

# Claude conversation files
claude_conversation.md
claude_session.json
.claude/
**/.claude/

# AI generated temporary files
ai_temp/
**/*_ai_temp*
**/*claude_temp*
**/*cursor_temp*

# ==============================================
# ADDITIONAL DEVELOPMENT FILES
# ==============================================

# Jupyter Notebook
backend/*.ipynb
backend/.ipynb_checkpoints

# Testing
backend/pytest.ini
backend/.pytest_cache/
backend/tests/__pycache__/

# Database files
backend/*.db
backend/*.sqlite
backend/*.sqlite3

# Backup files
backend/*.bak
backend/*.backup
backend/*.old

# Large media files
backend/uploads/
backend/media/
backend/static/uploads/

# Documentation builds
backend/docs/_build/

# IDE specific files
backend/.idea/
backend/*.sublime-project
backend/*.sublime-workspace

# ==============================================
# TEAM COLLABORATION
# ==============================================

# Personal notes (team members can create these)
NOTES.md
TODO.personal.md
.personal/
**/.personal/