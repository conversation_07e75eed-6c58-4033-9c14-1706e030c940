#!/usr/bin/env python3
"""
Google Places API Test Suite
Tests Place Details and Place Photo functionality using the new Places API
Documentation: https://developers.google.com/maps/documentation/places/web-service/place-details
"""

import asyncio
import sys
import os

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Import service functions
from app.services.googleplaces_service import (
    get_place_details,
    get_place_photo,
    get_place_summary,
    get_place_photos_info
)

async def test_google_places_api():
    """Test Google Places API functionality"""
    print("🗺️ Google Places API Test")
    print("Documentation: https://developers.google.com/maps/documentation/places/web-service/place-details")
    print()
    
    # Test place IDs
    test_places = [
        ("ChIJN1t_tDeuEmsRUsoyG83frY4", "Google Sydney Office"),
        ("ChIJrTLr-GyuEmsRBfy61i59si0", "Sydney Opera House"),
        ("ChIJOwg_06VPwokRYv534QaPC8g", "Empire State Building")
    ]
    
    for place_id, place_name in test_places:
        print(f"🔍 Testing: {place_name}")
        
        try:
            # Test place details
            details = await get_place_details(place_id)
            print(f"   ✅ Place Details: {details.result.name}")
            print(f"   📍 Address: {details.result.formatted_address}")
            print(f"   ⭐ Rating: {details.result.rating}/5.0 ({details.result.user_ratings_total} reviews)")
            
            # Test place summary
            summary = await get_place_summary(place_id)
            print(f"   📋 Summary: {summary['name']} - {summary['business_status']}")
            
            # Test photos info
            photos = await get_place_photos_info(place_id)
            print(f"   📸 Photos available: {len(photos)}")
            
            # Test photo download if available
            if photos:
                photo_ref = photos[0]["photo_reference"]
                photo_data = await get_place_photo(photo_ref, max_width=200)
                print(f"   🖼️ Downloaded photo: {len(photo_data)} bytes")
            
            print()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            print()
    
    print("✅ Google Places API tests completed!")

if __name__ == "__main__":
    asyncio.run(test_google_places_api())