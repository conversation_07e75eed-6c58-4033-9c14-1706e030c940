"use client";
import { useEffect, useState } from "react";
import { X, AlertTriangle } from "lucide-react";

interface ToastProps {
  id: string;
  title: string;
  message: string;
  variant: "warning" | "success" | "error" | "info";
  duration?: number;
  onClose: (id: string) => void;
}



// Toast管理器和Hook
interface ToastItem {
  id: string;
  title: string;
  message: string;
  variant: "warning" | "success" | "error" | "info";
  duration?: number;
}

export function useToast() {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const showToast = (toast: Omit<ToastItem, "id">) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { ...toast, id }]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return { toasts, showToast, removeToast };
}

// Toast容器组件
export function ToastContainer({ toasts, onRemove }: { toasts: ToastItem[]; onRemove: (id: string) => void }) {
  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[80] pointer-events-none">
      <div className="flex flex-col gap-2">
        {toasts.map((toast, index) => (
          <div 
            key={toast.id}
            className="pointer-events-auto"
            style={{ 
              transform: `translateY(${index * 8}px)`,
              zIndex: 80 - index 
            }}
          >
            <ToastItem
              {...toast}
              onClose={onRemove}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// 重命名原来的Toast组件为ToastItem
function ToastItem({ id, title, message, variant, duration = 5000, onClose }: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    // 进度条动画
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev - (100 / (duration / 50)); // 每50ms更新一次
        if (newProgress <= 0) {
          clearInterval(progressInterval);
          return 0;
        }
        return newProgress;
      });
    }, 50);

    // 自动关闭定时器
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose(id), 300); // 等待动画完成后再移除
    }, duration);

    return () => {
      clearTimeout(timer);
      clearInterval(progressInterval);
    };
  }, [duration, id, onClose]);

  const getVariantStyles = () => {
    switch (variant) {
      case "warning":
        return {
          bg: "bg-[#010066]", // 深海军蓝
          titleColor: "text-[#FFCC00]", // 金黄色
          textColor: "text-white", // 白色
          icon: <AlertTriangle className="w-5 h-5 text-[#FFCC00]" />
        };
      case "success":
        return {
          bg: "bg-[#28A745]",
          titleColor: "text-white",
          textColor: "text-white",
          icon: <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-[#28A745] rounded-full"></div>
          </div>
        };
      case "error":
        return {
          bg: "bg-[#CC0000]",
          titleColor: "text-white",
          textColor: "text-white",
          icon: <X className="w-5 h-5 text-white" />
        };
      default:
        return {
          bg: "bg-[#010066]",
          titleColor: "text-[#FFCC00]",
          textColor: "text-white",
          icon: <div className="w-5 h-5 bg-[#FFCC00] rounded-full"></div>
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div
      className={`max-w-md w-full mx-4 transition-all duration-300 ${
        isVisible ? "translate-y-0 opacity-100" : "-translate-y-2 opacity-0"
      }`}
      aria-live="assertive"
      role="alert"
    >
      <div className={`${styles.bg} rounded-xl shadow-lg border border-[#FFCC00]/20 p-4 relative overflow-hidden`}>
        <div className="flex items-start gap-3">
          {styles.icon}
          <div className="flex-1 min-w-0">
            <div className={`font-semibold text-sm ${styles.titleColor} mb-1`}>
              {title}
            </div>
            <div className={`text-sm ${styles.textColor} opacity-90`}>
              {message}
            </div>
          </div>
        </div>
        
        {/* 进度条 */}
        <div className="absolute bottom-0 left-0 w-full h-1 bg-black/20">
          <div 
            className="h-full bg-[#FFCC00] transition-all duration-75 ease-linear"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </div>
  );
}
