"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>t, Star, Check } from "lucide-react";

export default function SubscribePage() {
  const [email, setEmail] = useState("");

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: 集成邮件/支付功能
    alert(`Thank you for your interest! We'll notify ${email} when subscription is available.`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-myrNavy/5 via-white to-myrYellow/5">
      {/* Header */}
      <div className="container-page pt-8 pb-4">
        <Link 
          href="/"
          className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to SynTour
        </Link>
      </div>

      {/* Main Content */}
      <div className="container-page">
        <div className="max-w-2xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Star className="w-8 h-8 text-myrYellow fill-current" />
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-myrNavy to-myrYellow bg-clip-text text-transparent">
                Subscribe to SynTour
              </h1>
              <Star className="w-8 h-8 text-myrYellow fill-current" />
            </div>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              Unlock premium features and get priority access to the most advanced AI travel planning experience in Malaysia.
            </p>
          </div>

          {/* Features Preview */}
          <div className="grid md:grid-cols-2 gap-6 mb-12">
            <div className="p-6 bg-white rounded-2xl shadow-sm border border-gray-100">
              <div className="flex items-center gap-3 mb-3">
                <Check className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold text-gray-900">Unlimited AI Planning</h3>
              </div>
              <p className="text-gray-600 text-sm">
                Generate unlimited personalized itineraries with our advanced AI engine
              </p>
            </div>

            <div className="p-6 bg-white rounded-2xl shadow-sm border border-gray-100">
              <div className="flex items-center gap-3 mb-3">
                <Check className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold text-gray-900">Priority Support</h3>
              </div>
              <p className="text-gray-600 text-sm">
                Get priority access to our Malaysia travel experts
              </p>
            </div>

            <div className="p-6 bg-white rounded-2xl shadow-sm border border-gray-100">
              <div className="flex items-center gap-3 mb-3">
                <Check className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold text-gray-900">Exclusive Deals</h3>
              </div>
              <p className="text-gray-600 text-sm">
                Access to subscriber-only flight and hotel discounts
              </p>
            </div>

            <div className="p-6 bg-white rounded-2xl shadow-sm border border-gray-100">
              <div className="flex items-center gap-3 mb-3">
                <Check className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold text-gray-900">Advanced Features</h3>
              </div>
              <p className="text-gray-600 text-sm">
                Voice planning, real-time updates, and offline access
              </p>
            </div>
          </div>

          {/* CTA Form */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Get Notified When We Launch
            </h2>
            <p className="text-gray-600 mb-6">
              Be the first to know when SynTour Premium becomes available.
            </p>
            
            <form onSubmit={handleSubscribe} className="space-y-4">
              <div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-myrYellow/50 focus:border-myrYellow transition-colors"
                  required
                />
              </div>
              
              <button
                type="submit"
                className="w-full py-3 px-6 bg-gradient-to-r from-myrNavy to-myrYellow text-white font-semibold rounded-xl hover:scale-105 active:scale-95 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-myrYellow/50 focus:ring-offset-2 transition-all duration-200"
              >
                ✨ Notify Me When Available
              </button>
            </form>
            
            <p className="text-xs text-gray-500 mt-4">
              We&apos;ll only send you updates about SynTour Premium. No spam, ever.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
