from google import genai
import os

def simple_test():
    """测试脚本"""
    try:
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not project_id :
            print("❌ Missing required env var: GOOGLE_CLOUD_PROJECT")

        location = os.getenv("GOOGLE_CLOUD_LOCATION")
        if not location :
            print("❌ Missing required env var: GOOGLE_CLOUD_LOCATION")

        model_name = os.getenv("VERTEX_AI_ENDPOINT")
        if not model_name :
            print("❌ Missing required env var: VERTEX_AI_ENDPOINT")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )
        print("✅ Vertex AI 初始化成功")

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")
        print("✅ 端点连接成功!")

        print("\n🚀 发送测试请求...")
        response = client.models.generate_content(
            model=model_name, 
            contents="Who are you?"
        )

        ai_response = response.text if hasattr(response, "text") else str(response)
        print(f"🤖 AI Response:\n{ai_response}")

        print("Successed!")

    except Exception as e:
        print(f"❌ Failed to initialize Google Gen AI: {e}")
         
if __name__ == "__main__":
    simple_test()