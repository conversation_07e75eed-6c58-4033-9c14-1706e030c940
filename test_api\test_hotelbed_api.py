#!/usr/bin/env python3
"""
Hotelbeds Content API Test Script

This script tests the Hotelbeds Content API integration using the updated
service layer and models. It demonstrates various API endpoints and validates
the response structure against the Pydantic models.

Documentation: https://developer.hotelbeds.com/documentation/hotels/content-api/
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.hotelbeds_service import (
    get_hotels_list,
    get_hotel_details,
    get_destinations,
    get_countries,
    get_accommodations,
    get_categories,
    get_chains,
    get_facilities,
    hotelbeds_request
)
from app.models.hotelbeds_model import (
    HotelsResponse,
    HotelDetailsResponse,
    Hotel,
    HotelDetails,
    AuditData
)

class HotelbedsAPITester:
    """Test class for Hotelbeds Content API endpoints"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_hotels_list(self):
        """Test hotels list endpoint"""
        try:
            print("\n🏨 Testing Hotels List API...")
            
            # Test basic hotel list for Malaysia
            response = await get_hotels_list(
                country_code="MY",
                from_index=1,
                to_index=10,
                fields="basic",
                language="ENG"
            )
            
            # Validate response structure
            if "hotels" in response:
                hotels_count = len(response["hotels"])
                self.log_test(
                    "Hotels List - Basic Request",
                    True,
                    {"hotels_count": hotels_count, "country": "MY"}
                )
                
                # Test first hotel structure
                if hotels_count > 0:
                    first_hotel = response["hotels"][0]
                    # Basic response only guarantees 'code' field
                    required_fields = ["code"]
                    missing_fields = [field for field in required_fields if field not in first_hotel]
                    
                    self.log_test(
                        "Hotel Model Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields, 
                            "available_fields": list(first_hotel.keys()),
                            "sample_hotel_code": first_hotel.get("code")
                        }
                    )
                else:
                    self.log_test("Hotel Model Structure", False, {"error": "No hotels returned"})
            else:
                self.log_test("Hotels List - Basic Request", False, {"error": "No hotels key in response"})
            
            # Test with different parameters
            response_detailed = await get_hotels_list(
                country_code="SG",
                from_index=1,
                to_index=5,
                fields="all",
                language="ENG"
            )
            
            self.log_test(
                "Hotels List - Detailed Fields",
                "hotels" in response_detailed,
                {"country": "SG", "fields": "all"}
            )
            
        except Exception as e:
            self.log_test("Hotels List API", False, {"error": str(e)})
    
    async def test_hotel_details(self):
        """Test hotel details endpoint"""
        try:
            print("\n🏨 Testing Hotel Details API...")
            
            # First get a hotel code from the list
            hotels_response = await get_hotels_list(
                country_code="MY",
                from_index=1,
                to_index=5,
                fields="basic"
            )
            
            if "hotels" in hotels_response and len(hotels_response["hotels"]) > 0:
                hotel_code = hotels_response["hotels"][0]["code"]
                
                # Get detailed information
                details_response = await get_hotel_details(
                    hotel_code=hotel_code,
                    language="ENG"
                )
                
                if "hotel" in details_response:
                    hotel_details = details_response["hotel"]
                    required_fields = ["code"]  # Only code is guaranteed
                    has_required = all(field in hotel_details for field in required_fields)
                    
                    # Extract hotel name from nested structure
                    hotel_name = "N/A"
                    if "name" in hotel_details and hotel_details["name"]:
                        if isinstance(hotel_details["name"], dict) and "content" in hotel_details["name"]:
                            hotel_name = hotel_details["name"]["content"]
                        else:
                            hotel_name = str(hotel_details["name"])
                    
                    self.log_test(
                        "Hotel Details - Structure",
                        has_required,
                        {
                            "hotel_code": hotel_code,
                            "hotel_name": hotel_name,
                            "has_facilities": "facilities" in hotel_details,
                            "has_rooms": "rooms" in hotel_details,
                            "available_fields": list(hotel_details.keys())[:10]  # First 10 fields
                        }
                    )
                else:
                    self.log_test("Hotel Details - Structure", False, {"error": "No hotel key in response"})
            else:
                self.log_test("Hotel Details API", False, {"error": "No hotels available for testing"})
                
        except Exception as e:
            self.log_test("Hotel Details API", False, {"error": str(e)})
    
    async def test_locations_endpoints(self):
        """Test location-related endpoints"""
        try:
            print("\n🌍 Testing Location APIs...")
            
            # Test destinations
            destinations = await get_destinations(language="ENG")
            self.log_test(
                "Destinations API",
                "destinations" in destinations or isinstance(destinations, list),
                {"response_type": type(destinations).__name__}
            )
            
            # Test countries
            countries = await get_countries(language="ENG")
            self.log_test(
                "Countries API",
                "countries" in countries or isinstance(countries, list),
                {"response_type": type(countries).__name__}
            )
            
            # Test destinations with country filter
            my_destinations = await get_destinations(country_code="MY", language="ENG")
            self.log_test(
                "Destinations with Country Filter",
                "destinations" in my_destinations or isinstance(my_destinations, list),
                {"country": "MY"}
            )
            
        except Exception as e:
            self.log_test("Location APIs", False, {"error": str(e)})
    
    async def test_types_endpoints(self):
        """Test type-related endpoints (accommodations, categories, etc.)"""
        try:
            print("\n🏷️ Testing Types APIs...")
            
            # Test accommodations
            accommodations = await get_accommodations(language="ENG")
            self.log_test(
                "Accommodations API",
                "accommodations" in accommodations or isinstance(accommodations, list),
                {"response_type": type(accommodations).__name__}
            )
            
            # Test categories
            categories = await get_categories(language="ENG")
            self.log_test(
                "Categories API",
                "categories" in categories or isinstance(categories, list),
                {"response_type": type(categories).__name__}
            )
            
            # Test chains
            chains = await get_chains(language="ENG")
            self.log_test(
                "Chains API",
                "chains" in chains or isinstance(chains, list),
                {"response_type": type(chains).__name__}
            )
            
            # Test facilities
            facilities = await get_facilities(language="ENG")
            self.log_test(
                "Facilities API",
                "facilities" in facilities or isinstance(facilities, list),
                {"response_type": type(facilities).__name__}
            )
            
        except Exception as e:
            self.log_test("Types APIs", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid hotel code
            try:
                response = await get_hotel_details(hotel_code=999999999, language="ENG")
                # Check if response indicates an error (empty or error structure)
                if "hotel" not in response or not response["hotel"]:
                    self.log_test("Error Handling - Invalid Hotel Code", True, {"response": "No hotel data returned"})
                else:
                    self.log_test("Error Handling - Invalid Hotel Code", True, {"response": "API handled gracefully"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Hotel Code", True, {"expected_error": str(e)})
            
            # Test invalid country code
            try:
                response = await get_hotels_list(country_code="INVALID", from_index=1, to_index=5)
                # Some APIs might return empty results instead of errors
                self.log_test("Error Handling - Invalid Country", True, {"response": "Handled gracefully"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Country", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def test_pagination(self):
        """Test pagination functionality"""
        try:
            print("\n📄 Testing Pagination...")
            
            # Test different page sizes
            small_page = await get_hotels_list(
                country_code="MY",
                from_index=1,
                to_index=5,
                fields="basic"
            )
            
            large_page = await get_hotels_list(
                country_code="MY",
                from_index=1,
                to_index=20,
                fields="basic"
            )
            
            small_count = len(small_page.get("hotels", []))
            large_count = len(large_page.get("hotels", []))
            
            self.log_test(
                "Pagination - Different Page Sizes",
                large_count >= small_count,
                {"small_page": small_count, "large_page": large_count}
            )
            
            # Test pagination metadata
            if "total" in large_page:
                self.log_test(
                    "Pagination - Metadata",
                    True,
                    {
                        "total": large_page.get("total"),
                        "from": large_page.get("from"),
                        "to": large_page.get("to")
                    }
                )
            else:
                self.log_test("Pagination - Metadata", False, {"error": "No pagination metadata"})
            
        except Exception as e:
            self.log_test("Pagination Tests", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Hotelbeds Content API Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_hotels_list()
        await self.test_hotel_details()
        await self.test_locations_endpoints()
        await self.test_types_endpoints()
        await self.test_pagination()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("hotelbeds_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: hotelbeds_test_results.json")

async def main():
    """Main test runner"""
    print("🏨 Hotelbeds Content API Test Suite")
    print("Documentation: https://developer.hotelbeds.com/documentation/hotels/content-api/")
    print()
    
    # Check environment variables
    required_env_vars = ["HOTELBEDS_API_KEY", "HOTELBEDS_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('HOTELBEDS_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = HotelbedsAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())