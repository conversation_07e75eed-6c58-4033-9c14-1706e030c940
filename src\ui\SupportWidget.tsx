"use client";
import { useState, useEffect } from "react";
import { Mail, User, MessageCircle, X } from "lucide-react";

export default function SupportWidget(){
  const [open, setOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [msg, setMsg] = useState("");
  const [buttonPressed, setButtonPressed] = useState(false);

  // 添加点击外部区域关闭支持面板的功能
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as Element;
      if (open && !target.closest('[data-support-widget]')) {
        setOpen(false);
      }
    }

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [open]);

  const supportAddress = "<EMAIL>"; // TODO: change to your address
  
  function openMailto(){
    const subject = encodeURIComponent("SynTour Support - Malaysia Travel Assistance");
    const body = encodeURIComponent(`Hello SynTour Support Team,

I need assistance with my Malaysia travel planning:

Email: ${email}
Message: ${msg}

Thank you for your help!

Best regards,
${email.split('@')[0] || 'Traveler'}`);
    
    window.location.href = `mailto:${supportAddress}?subject=${subject}&body=${body}`;
  }

  function openDirectEmail(){
    window.open(`mailto:${supportAddress}?subject=SynTour Support - Malaysia Travel Assistance`, '_blank');
  }

  return (
    <>
      {/* Malaysian Customer Service Floating Button - 移动到右侧聊天框上方 */}
      <button
        onClick={() => setOpen(!open)}
        onMouseDown={() => setButtonPressed(true)}
        onMouseUp={() => setButtonPressed(false)}
        onMouseLeave={() => setButtonPressed(false)}
        className={`fixed right-4 bottom-32 md:bottom-36 lg:bottom-40 w-14 h-14 md:w-16 md:h-16 lg:w-18 lg:h-18 rounded-full shadow-xl border-2 border-white transition-all duration-300 z-50 transform ${
          buttonPressed ? 'scale-95 shadow-2xl' : 'hover:scale-110 hover:shadow-2xl'
        }`}
        style={{
          background: "linear-gradient(135deg, var(--my-primary), var(--my-blue-600), var(--my-secondary))",
          boxShadow: "var(--malaysia-shadow)"
        }}
        aria-label="Open customer service"
      >
        <div className="w-full h-full rounded-full overflow-hidden flex items-center justify-center">
          <img 
            src="/images/Travel customer service.png" 
            alt="Customer Service" 
            className="w-full h-full object-cover"
          />
        </div>
      </button>

      {/* Support Panel - 调整底部位置以适应更小的按钮 */}
      <div
        data-support-widget
        className={`fixed right-4 bottom-20 md:bottom-22 lg:bottom-24 w-[340px] max-w-[90vw] rounded-2xl bg-white shadow-xl border transition-all duration-300 z-[60] ${open ? "opacity-100 translate-y-0 pointer-events-auto" : "opacity-0 translate-y-2 pointer-events-none"}`}
      >
        <div className="p-5">
          {/* Header with Larger Icon and Close Button */}
          <div className="flex items-center gap-4 mb-4">
            <div className="w-14 h-14 md:w-16 md:h-16 lg:w-18 lg:h-18 rounded-full overflow-hidden flex-shrink-0">
              <img 
                src="/images/Travel customer service.png" 
                alt="Customer Service" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-bold text-lg md:text-xl text-my-ink">Malaysian Customer Service</div>
              <div className="text-sm text-gray-600">Selamat datang! How can we help?</div>
            </div>
            <button
              onClick={() => setOpen(false)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Close support panel"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-4">Our friendly Malaysian support team is here to assist you with your travel planning needs.</p>

          {/* Email Form */}
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-700">Your Email</label>
              <input 
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                placeholder="<EMAIL>" 
                value={email} 
                onChange={(e)=>setEmail(e.target.value)} 
              />
            </div>

            <div>
              <label className="text-xs font-medium text-gray-700">Message</label>
              <textarea 
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24 resize-none" 
                placeholder="Tell us about your Malaysia travel plans or any questions you have..." 
                value={msg} 
                onChange={(e)=>setMsg(e.target.value)} 
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3 mt-5">
            <button
              className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 flex items-center justify-center gap-2"
              onClick={openMailto}
            >
              <Mail className="w-4 h-4" />
              Send Email
            </button>
            <button 
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              onClick={openDirectEmail}
            >
              <MessageCircle className="w-4 h-4" />
            </button>
            <button 
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              onClick={()=>setOpen(false)}
            >
              Close
            </button>
          </div>

          {/* Quick Contact Info */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-600">
              <div className="flex items-center gap-2 mb-1">
                <Mail className="w-3 h-3" />
                <span className="font-medium">Quick Contact:</span>
              </div>
              <div className="text-gray-700">{supportAddress}</div>
            </div>
          </div>

          <p className="text-[11px] text-gray-500 mt-3 text-center">
            We&apos;ll respond within 24 hours. Terima kasih! 🙏
          </p>
        </div>
      </div>
    </>
  );
}
