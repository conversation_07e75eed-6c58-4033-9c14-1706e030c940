"use client";
import { useMemo, useState } from "react";
import { Minus, Plus, HelpCircle } from "lucide-react";
import PreferenceTags from "./PreferenceTags";

/** +/- slider for Budget & Duration only */
function NumberSlider({
  value, set, min, max, step=1,
}:{
  value:number; set:(n:number)=>void; min:number; max:number; step?:number;
}) {
  const dec = ()=> set(Math.max(min, value-step));
  const inc = ()=> set(Math.min(max, value+step));
  return (
    <div className="flex items-center gap-3">
      <button type="button" aria-label="decrease"
        className="w-9 h-9 rounded-full border bg-white hover:bg-gray-50 flex items-center justify-center"
        onClick={dec}><Minus className="w-4 h-4"/></button>
      <input type="range" min={min} max={max} step={step} value={value}
        onChange={(e)=>set(Number(e.target.value))}
        className="flex-1 accent-[color:var(--my-gold)]" />
      <button type="button" aria-label="increase"
        className="w-9 h-9 rounded-full border bg-white hover:bg-gray-50 flex items-center justify-center"
        onClick={inc}><Plus className="w-4 h-4"/></button>
    </div>
  );
}

/** Section header with Not sure? toggle + Unsure badge */
function SectionHeader({ title, unsure, setUnsure }:{
  title:string; unsure:boolean; setUnsure:(v:boolean)=>void;
}) {
  return (
    <div className="flex items-center justify-between mb-2">
      <div className="text-sm font-medium flex items-center gap-2">
        {title}
        {unsure && (
          <span className="px-2 py-0.5 text-[10px] rounded-full bg-amber-100 text-amber-700 border border-amber-200">
            Unsure
          </span>
        )}
      </div>
      <button
        type="button"
        onClick={()=>setUnsure(!unsure)}
        className={`text-xs inline-flex items-center gap-1 px-2 py-1 rounded-md border transition
          ${unsure
            ? "border-amber-300 text-amber-700 bg-amber-50 hover:bg-amber-100"
            : "border-gray-300 text-gray-600 hover:bg-gray-50"}`}
        aria-pressed={unsure}
      >
        <HelpCircle className="w-3.5 h-3.5" />
        Not sure?
      </button>
    </div>
  );
}

export default function PlannerWizard({ onSubmit }:{ onSubmit?:(answers:any)=>void }) {
  /* Answers */
  const [pax, setPax] = useState(2);
  const [pace, setPace] = useState("Moderate");
  const [budget, setBudget] = useState(5000);
  const [days, setDays] = useState(7);
  const [styles, setStyles] = useState<string[]>(["Culture"]);
  const [foods, setFoods] = useState<string[]>(["Local Malay"]);
  const [regions, setRegions] = useState<string[]>(["Kuala Lumpur"]);

  /* Per-section unsure toggles */
  const [uTravelers, setUTrav] = useState(false);
  const [uPace, setUPace] = useState(false);
  const [uBudget, setUBudget] = useState(false);
  const [uDays, setUDays] = useState(false);
  const [uStyles, setUStyles] = useState(false);
  const [uFoods, setUFoods] = useState(false);
  const [uRegions, setURegions] = useState(false);

  const promptText = useMemo(()=>{
    const parts:string[] = [];
    parts.push(`Plan a ${days}-day trip in Malaysia for ${pax} ${pax>1?"people":"person"} with a ${pace.toLowerCase()} pace and a budget of MYR ${budget}.`);
    if (regions.length) parts.push(`Preferred regions: ${regions.join(", ")}.`);
    if (styles.length)  parts.push(`Travel style: ${styles.join(", ")}.`);
    if (foods.length)   parts.push(`Food preferences: ${foods.join(", ")}.`);

    const unsureNotes:string[] = [];
    if (uTravelers) unsureNotes.push("traveler count");
    if (uPace)      unsureNotes.push("travel pace");
    if (uBudget)    unsureNotes.push("budget (please offer tiered options)");
    if (uDays)      unsureNotes.push("duration (suggest several lengths)");
    if (uStyles)    unsureNotes.push("travel style (provide a few mixes)");
    if (uFoods)     unsureNotes.push("food preferences (varied cuisines)");
    if (uRegions)   unsureNotes.push("regions (propose destinations)");
    if (unsureNotes.length) {
      parts.push(`I&apos;m not sure about: ${unsureNotes.join(", ")}. Please propose alternatives for those.`);
    }
    parts.push("Please return a day-by-day itinerary with attractions, food, transport and rough costs.");
    return parts.join(" ");
  },[pax, pace, budget, days, styles, foods, regions, uTravelers, uPace, uBudget, uDays, uStyles, uFoods, uRegions]);

  function fillChat() {
    window.dispatchEvent(new CustomEvent("mytour.compose", { detail: promptText })); // fill only, no auto-send
    onSubmit?.({
      pax, pace, budget, days, styles, foods, regions,
      unsure: { uTravelers, uPace, uBudget, uDays, uStyles, uFoods, uRegions }
    });
  }

  return (
    <div className="p-5 space-y-5">
      <p className="text-sm text-gray-600">Answer a few quick questions and we&apos;ll tailor your plan. You can skip any step.</p>

      {/* 1. Travelers */}
      <div>
        <SectionHeader title="Number of Travelers" unsure={uTravelers} setUnsure={setUTrav}/>
        <div className="grid grid-cols-2 gap-2">
          {["Solo","Couple","Family","Group"].map(opt => {
            const isSelected = (opt==="Solo" && pax===1) || (opt==="Couple" && pax===2) || (opt==="Family" && pax===4) || (opt==="Group" && pax===6);
            return (
              <button key={opt}
                onClick={()=> setPax(opt==="Solo"?1: opt==="Couple"?2: opt==="Family"?4: 6)}
                className={`tile py-3 transition-all duration-200 transform hover:scale-105 active:scale-95 ${
                  isSelected 
                    ? "ring-2 ring-[color:var(--my-gold)] bg-gradient-to-br from-blue-50 to-purple-50 shadow-md" 
                    : "hover:shadow-md hover:bg-gray-50"
                }`}>
                <div className={`text-sm text-center font-medium ${
                  isSelected ? "text-blue-700" : "text-gray-800"
                }`}>{opt}</div>
              </button>
            );
          })}
        </div>
      </div>

      {/* 2. Pace */}
      <div>
        <SectionHeader title="Travel Pace" unsure={uPace} setUnsure={setUPace}/>
        <div className="grid grid-cols-2 gap-2">
          {["Relaxed","Moderate","Active","Intense"].map(p => {
            const isSelected = pace === p;
            return (
              <button key={p} onClick={()=>setPace(p)}
                className={`tile py-3 transition-all duration-200 transform hover:scale-105 active:scale-95 ${
                  isSelected 
                    ? "ring-2 ring-[color:var(--my-gold)] bg-gradient-to-br from-green-50 to-blue-50 shadow-md" 
                    : "hover:shadow-md hover:bg-gray-50"
                }`}>
                <div className={`text-sm text-center font-medium ${
                  isSelected ? "text-green-700" : "text-gray-800"
                }`}>{p}</div>
              </button>
            );
          })}
        </div>
      </div>

      {/* 3. Budget */}
      <div>
        <SectionHeader title="Budget (MYR)" unsure={uBudget} setUnsure={setUBudget}/>
        <div className="mb-1 text-xs text-gray-500">Current: MYR {budget}</div>
        <NumberSlider value={budget} set={setBudget} min={300} max={20000} step={100}/>
      </div>

      {/* 4. Duration */}
      <div>
        <SectionHeader title="Duration (days)" unsure={uDays} setUnsure={setUDays}/>
        <div className="mb-1 text-xs text-gray-500">Current: {days} day(s)</div>
        <NumberSlider value={days} set={setDays} min={1} max={30} step={1}/>
      </div>

      {/* 5. Travel Style */}
      <div>
        <SectionHeader title="Travel Style" unsure={uStyles} setUnsure={setUStyles}/>
        <PreferenceTags
          title=""
          options={["Relax","Foodie","Nature","Culture","Shopping","Island","City","Adventure","Nightlife"]}
          selected={styles}
          onChange={setStyles}
        />
      </div>

      {/* 6. Food & Cuisine */}
      <div>
        <SectionHeader title="Food & Cuisine" unsure={uFoods} setUnsure={setUFoods}/>
        <PreferenceTags
          title=""
          options={["Local Malay","Chinese","Indian","Seafood","Street Food","Halal","Vegetarian","Cafe Hopping","Fusion","Durian"]}
          selected={foods}
          onChange={setFoods}
        />
      </div>

      {/* 7. Preferred Regions */}
      <div>
        <SectionHeader title="Preferred Regions" unsure={uRegions} setUnsure={setURegions}/>
        <PreferenceTags
          title=""
          options={["Kuala Lumpur","Penang","Langkawi","Melacca","Sabah","Sarawak","Cameron","Ipoh"]}
          selected={regions}
          onChange={setRegions}
        />
      </div>

      {/* Spacer */}
      <div className="h-4"></div>

      {/* Primary CTA - Create My Malaysia Adventure! */}
      <div className="bg-gradient-to-r from-my-blue-50 to-my-gold-50 p-4 rounded-xl border border-my-blue-200">
        <button
          onClick={fillChat}
          className="btn w-full text-white text-lg font-semibold py-4 shadow-malaysia hover:shadow-gold transition-all duration-300"
          style={{ background: "linear-gradient(135deg, var(--my-primary), var(--my-blue-600), var(--my-secondary))" }}
        >
          ✈️ Create My Malaysia Adventure!
        </button>
        <p className="text-xs text-gray-600 mt-2 text-center">
          Click to fill chat with your personalized travel request
        </p>
      </div>

      {/* Preview */}
      <div className="text-xs text-gray-600">
        <div className="font-medium mb-1">Preview:</div>
        <div className="p-3 rounded-lg bg-[color:var(--my-bg)] border">{promptText}</div>
      </div>
    </div>
  );
}
