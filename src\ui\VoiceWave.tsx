// src/ui/VoiceWave.tsx
import React from 'react';

interface VoiceWaveProps {
  className?: string;
}

export default function VoiceWave({ className = "w-4 h-4" }: VoiceWaveProps) {
  return (
    <svg 
      className={className} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="4" y="6" width="2" height="12" rx="1" fill="currentColor">
        <animate
          attributeName="height"
          values="12;4;12"
          dur="1.5s"
          repeatCount="indefinite"
        />
        <animate
          attributeName="y"
          values="6;10;6"
          dur="1.5s"
          repeatCount="indefinite"
        />
      </rect>
      <rect x="8" y="4" width="2" height="16" rx="1" fill="currentColor">
        <animate
          attributeName="height"
          values="16;8;16"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.3s"
        />
        <animate
          attributeName="y"
          values="4;8;4"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.3s"
        />
      </rect>
      <rect x="12" y="8" width="2" height="8" rx="1" fill="currentColor">
        <animate
          attributeName="height"
          values="8;16;8"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.6s"
        />
        <animate
          attributeName="y"
          values="8;4;8"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.6s"
        />
      </rect>
      <rect x="16" y="6" width="2" height="12" rx="1" fill="currentColor">
        <animate
          attributeName="height"
          values="12;4;12"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.9s"
        />
        <animate
          attributeName="y"
          values="6;10;6"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.9s"
        />
      </rect>
    </svg>
  );
}
