# SynTour项目部署和测试指南

## 🚀 快速启动

### 1. 环境准备

#### 后端环境
```bash
cd backend
pip install -r requirements-core.txt
```

#### 前端环境
```bash
npm install
# 或
yarn install
```

### 2. 环境变量配置

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入实际配置：
```env
# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8002
NEXT_PUBLIC_WS_URL=ws://localhost:8002

# Google Cloud配置
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=./backend/config/service-account-key.json

# Vertex AI配置
VERTEX_AI_ENDPOINT=your-vertex-ai-endpoint
```

### 3. 启动服务

#### 启动后端服务
```bash
cd backend
python main_enhanced.py
```
后端将在 `http://localhost:8002` 启动

#### 启动前端服务
```bash
npm run dev
# 或
yarn dev
```
前端将在 `http://localhost:3007` 启动

## 🧪 功能测试

### 自动化测试
运行修复验证脚本：
```bash
python test_fixes.py
```

### 手动测试清单

#### ✅ 基础功能测试
- [ ] 访问 `http://localhost:3007` 页面正常加载
- [ ] 后端健康检查 `http://localhost:8002/health` 返回正常
- [ ] 前端可以正常显示聊天界面

#### ✅ 文本聊天功能
- [ ] 输入文本消息并发送
- [ ] 收到AI回复
- [ ] 错误情况下显示友好错误信息

#### ✅ 图片上传功能
- [ ] 点击图片上传按钮
- [ ] 选择图片文件（支持jpg, png, gif等）
- [ ] 图片预览正常显示
- [ ] 发送图片+文本消息
- [ ] 收到AI对图片的分析回复

#### ✅ 语音识别功能
- [ ] 点击麦克风按钮开始录音
- [ ] 浏览器请求麦克风权限
- [ ] 录音状态指示正常（红色圆点）
- [ ] 说话时能看到实时转录文本
- [ ] 停止录音后文本自动填入输入框
- [ ] 多次使用语音功能无内存泄漏

#### ✅ 错误处理测试
- [ ] 网络断开时显示连接错误
- [ ] 上传超大文件时显示大小限制错误
- [ ] 上传不支持格式时显示格式错误
- [ ] WebSocket断开时能自动重连或显示错误

## 🔧 故障排除

### 常见问题

#### 1. 后端启动失败
**症状**: `python main_enhanced.py` 报错
**解决方案**:
- 检查Python版本（需要3.8+）
- 安装缺失的依赖：`pip install -r requirements-core.txt`
- 检查Google Cloud凭据配置

#### 2. 前端连接后端失败
**症状**: 前端显示连接错误
**解决方案**:
- 确认后端服务正在运行
- 检查 `.env` 文件中的API URL配置
- 检查防火墙设置

#### 3. 语音功能不工作
**症状**: 点击麦克风无反应或报错
**解决方案**:
- 确认浏览器支持WebRTC
- 检查麦克风权限
- 使用HTTPS（生产环境必需）
- 检查WebSocket连接

#### 4. 图片上传失败
**症状**: 图片上传后无响应或报错
**解决方案**:
- 检查文件大小（限制10MB）
- 确认文件格式支持
- 检查Google Cloud配置

### 日志查看

#### 后端日志
后端日志直接在控制台输出，关注以下信息：
- `✅ Vertex AI 初始化成功`
- `🚀 FastAPI server started successfully`
- WebSocket连接和断开日志

#### 前端日志
打开浏览器开发者工具（F12），查看Console标签：
- WebSocket连接状态
- 音频录制状态
- API请求响应

## 📊 性能监控

### 内存使用监控
- 使用浏览器任务管理器监控内存使用
- 长时间使用语音功能后检查内存是否持续增长
- 正常情况下停止录音后内存应该释放

### 连接监控
- 监控WebSocket连接数量
- 检查连接是否正常关闭
- 观察服务器资源使用情况

## 🔒 安全注意事项

### 生产环境配置
1. **HTTPS**: 生产环境必须使用HTTPS
2. **CORS**: 限制允许的域名
3. **API密钥**: 保护Google Cloud API密钥
4. **文件上传**: 添加文件内容扫描

### 环境变量安全
- 不要将 `.env` 文件提交到版本控制
- 使用环境变量管理敏感信息
- 定期轮换API密钥

## 📈 扩展部署

### Docker部署
```dockerfile
# 后端Dockerfile示例
FROM python:3.9-slim
WORKDIR /app
COPY requirements-core.txt .
RUN pip install -r requirements-core.txt
COPY . .
EXPOSE 8002
CMD ["python", "main_enhanced.py"]
```

### 负载均衡
- 使用Nginx进行负载均衡
- 配置WebSocket代理
- 实现会话粘性（sticky sessions）

## 📞 支持联系

如果遇到问题：
1. 首先查看本文档的故障排除部分
2. 检查日志输出
3. 运行自动化测试脚本
4. 查看项目的GitHub Issues

---

**最后更新**: 2025-08-26
**版本**: 2.0.0 (Bug修复版本)
