#!/bin/bash
echo "========================================"
echo "SynTour Debug Commands"
echo "========================================"

echo ""
echo "1. Checking if backend is running on port 8002..."
if netstat -an 2>/dev/null | grep -q :8002; then
    echo "✅ Service found on port 8002"
    netstat -an | grep :8002
else
    echo "❌ No service found on port 8002"
fi

echo ""
echo "2. Testing backend health endpoint..."
if curl -s http://localhost:8002/ >/dev/null 2>&1; then
    echo "✅ Backend responded"
    echo "Response:"
    curl -s http://localhost:8002/ | head -5
else
    echo "❌ Backend health check failed"
fi

echo ""
echo "3. Testing WebSocket connection (basic)..."
echo "This will attempt a basic WebSocket handshake:"
if curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://localhost:8002/ws/speech 2>/dev/null | head -1 | grep -q "101"; then
    echo "✅ WebSocket handshake successful"
else
    echo "❌ WebSocket handshake failed"
fi

echo ""
echo "4. Checking Google Cloud credentials..."
if [ -f "C:/Users/<USER>/Desktop/TRAVEL_AI/silver-course-470107-h0-de21f66e46dc.json" ]; then
    echo "✅ Google Cloud credentials file found"
else
    echo "❌ Google Cloud credentials file NOT found"
    echo "Expected location: C:/Users/<USER>/Desktop/TRAVEL_AI/silver-course-470107-h0-de21f66e46dc.json"
fi

echo ""
echo "5. Checking Python dependencies..."
python -c "import google.cloud.speech; print('✅ Google Cloud Speech library available')" 2>/dev/null || echo "❌ Google Cloud Speech library not available"
python -c "import fastapi; print('✅ FastAPI available')" 2>/dev/null || echo "❌ FastAPI not available"
python -c "import websockets; print('✅ WebSockets library available')" 2>/dev/null || echo "❌ WebSockets library not available"

echo ""
echo "6. Testing WebSocket with Python..."
echo "Running Python WebSocket test..."
python test_websocket.py

echo ""
echo "========================================"
echo "Debug completed"
echo "========================================"