#!/usr/bin/env python3
"""
Geoapify API Test Script - Location API and LocationDetails API

This script tests the Geoapify Location API integration using the updated
service layer and models. It demonstrates location search and details endpoints.

Documentation: 
1. https://apidocs.geoapify.com/docs/places/#api
2. https://apidocs.geoapify.com/docs/place-details/#place-details
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.geoapify_service import (
    search_locations,
    search_locations_by_coordinates,
    get_location_details,
    autocomplete_locations,
    extract_location_summary,
    validate_coordinates,
    validate_place_categories
)
from app.models.geoapify_model import (
    LocationSearchResponse,
    LocationDetailsResponse,
    PlaceType,
    extract_coordinates,
    format_address,
    is_business_location
)

class GeoapifyAPITester:
    """Test class for Geoapify Location API"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
        self.sample_place_id = None  # Store for details testing
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_location_search(self):
        """Test location search functionality"""
        try:
            print("\n🔍 Testing Location Search...")
            
            # Test basic location search
            response = await search_locations(
                text="restaurants in Paris",
                limit=5
            )
            
            # Validate response structure
            if "features" in response and len(response["features"]) > 0:
                features = response["features"]
                first_feature = features[0]
                
                # Store a place_id for details testing
                if "properties" in first_feature and "place_id" in first_feature["properties"]:
                    self.sample_place_id = first_feature["properties"]["place_id"]
                
                self.log_test(
                    "Location Search - Basic Query",
                    True,
                    {
                        "query": "restaurants in Paris",
                        "results_count": len(features),
                        "first_result": first_feature.get("properties", {}).get("name"),
                        "has_coordinates": "geometry" in first_feature
                    }
                )
                
                # Test response structure
                required_fields = ["type", "features"]
                missing_fields = [field for field in required_fields if field not in response]
                
                self.log_test(
                    "Location Search - Response Structure",
                    len(missing_fields) == 0,
                    {
                        "missing_fields": missing_fields,
                        "response_type": response.get("type"),
                        "features_count": len(features)
                    }
                )
                
                # Test feature structure
                if features:
                    feature = features[0]
                    feature_fields = ["type", "geometry", "properties"]
                    missing_feature_fields = [field for field in feature_fields if field not in feature]
                    
                    self.log_test(
                        "Location Search - Feature Structure",
                        len(missing_feature_fields) == 0,
                        {
                            "missing_fields": missing_feature_fields,
                            "geometry_type": feature.get("geometry", {}).get("type"),
                            "has_coordinates": "coordinates" in feature.get("geometry", {})
                        }
                    )
            else:
                self.log_test("Location Search - Basic Query", False, {"error": "No features in response"})
            
            # Test search with categories
            category_response = await search_locations(
                text="hotels in London",
                limit=3,
                categories=["accommodation"]
            )
            
            self.log_test(
                "Location Search - With Categories",
                "features" in category_response and len(category_response["features"]) > 0,
                {
                    "query": "hotels in London",
                    "category": "accommodation",
                    "results_count": len(category_response.get("features", []))
                }
            )
            
            # Test search with bias location
            bias_response = await search_locations(
                text="coffee shops",
                limit=3,
                bias_location=(48.8566, 2.3522)  # Paris coordinates
            )
            
            self.log_test(
                "Location Search - With Bias Location",
                "features" in bias_response,
                {
                    "query": "coffee shops",
                    "bias_location": "Paris (48.8566, 2.3522)",
                    "results_count": len(bias_response.get("features", []))
                }
            )
            
        except Exception as e:
            self.log_test("Location Search API", False, {"error": str(e)})
    
    async def test_nearby_search(self):
        """Test nearby location search"""
        try:
            print("\n📍 Testing Nearby Location Search...")
            
            # Test nearby search (Times Square, NYC)
            response = await search_locations_by_coordinates(
                lat=40.7580,
                lon=-73.9855,
                radius=500,
                limit=5,
                categories=["commercial", "catering"]
            )
            
            if "features" in response:
                features = response["features"]
                
                self.log_test(
                    "Nearby Search - Times Square",
                    len(features) > 0,
                    {
                        "location": "Times Square, NYC",
                        "radius": "500m",
                        "categories": ["commercial", "catering"],
                        "results_count": len(features)
                    }
                )
                
                # Test coordinate proximity (rough check)
                if features:
                    first_feature = features[0]
                    if "geometry" in first_feature and "coordinates" in first_feature["geometry"]:
                        coords = first_feature["geometry"]["coordinates"]
                        feature_lat, feature_lon = coords[1], coords[0]
                        
                        # Check if result is reasonably close to search center
                        lat_diff = abs(feature_lat - 40.7580)
                        lon_diff = abs(feature_lon - (-73.9855))
                        
                        self.log_test(
                            "Nearby Search - Proximity Check",
                            lat_diff < 0.01 and lon_diff < 0.01,  # Within ~1km
                            {
                                "search_center": (40.7580, -73.9855),
                                "result_coords": (feature_lat, feature_lon),
                                "lat_diff": lat_diff,
                                "lon_diff": lon_diff
                            }
                        )
            else:
                self.log_test("Nearby Search - Times Square", False, {"error": "No features in response"})
            
            # Test nearby search with larger radius
            large_radius_response = await search_locations_by_coordinates(
                lat=51.5074,  # London
                lon=-0.1278,
                radius=2000,
                limit=10
            )
            
            self.log_test(
                "Nearby Search - Large Radius",
                "features" in large_radius_response,
                {
                    "location": "London",
                    "radius": "2000m",
                    "results_count": len(large_radius_response.get("features", []))
                }
            )
            
        except Exception as e:
            self.log_test("Nearby Search API", False, {"error": str(e)})
    
    async def test_location_details(self):
        """Test location details functionality"""
        try:
            print("\n📋 Testing Location Details...")
            
            if not self.sample_place_id:
                # Try to get a place_id from a simple search
                search_response = await search_locations("Eiffel Tower", limit=1)
                if "features" in search_response and len(search_response["features"]) > 0:
                    props = search_response["features"][0].get("properties", {})
                    self.sample_place_id = props.get("place_id")
            
            if self.sample_place_id:
                # Test location details
                details_response = await get_location_details(self.sample_place_id)
                
                if "features" in details_response and len(details_response["features"]) > 0:
                    feature = details_response["features"][0]
                    properties = feature.get("properties", {})
                    
                    self.log_test(
                        "Location Details - Valid Place ID",
                        True,
                        {
                            "place_id": self.sample_place_id,
                            "name": properties.get("name"),
                            "formatted_address": properties.get("formatted"),
                            "has_coordinates": "geometry" in feature,
                            "available_fields": list(properties.keys())[:10]
                        }
                    )
                    
                    # Test essential fields presence
                    essential_fields = ["name", "formatted"]
                    available_essential = [field for field in essential_fields if field in properties]
                    
                    self.log_test(
                        "Location Details - Essential Fields",
                        len(available_essential) >= 1,
                        {
                            "essential_fields": essential_fields,
                            "available_essential": available_essential,
                            "coverage": f"{len(available_essential)}/{len(essential_fields)}"
                        }
                    )
                else:
                    self.log_test("Location Details - Valid Place ID", False, {"error": "No features in details response"})
            else:
                self.log_test("Location Details - Valid Place ID", False, {"error": "No place_id available for testing"})
            
        except Exception as e:
            self.log_test("Location Details API", False, {"error": str(e)})
    
    async def test_autocomplete(self):
        """Test autocomplete functionality"""
        try:
            print("\n💭 Testing Autocomplete...")
            
            # Test basic autocomplete
            response = await autocomplete_locations(
                text="New Y",
                limit=5
            )
            
            if "features" in response:
                features = response["features"]
                
                self.log_test(
                    "Autocomplete - Basic Query",
                    len(features) > 0,
                    {
                        "query": "New Y",
                        "suggestions_count": len(features),
                        "first_suggestion": features[0].get("properties", {}).get("name") if features else None
                    }
                )
                
                # Check if suggestions are relevant
                if features:
                    relevant_suggestions = 0
                    for feature in features:
                        name = feature.get("properties", {}).get("name", "").lower()
                        formatted = feature.get("properties", {}).get("formatted", "").lower()
                        if "new y" in name or "new y" in formatted:
                            relevant_suggestions += 1
                    
                    self.log_test(
                        "Autocomplete - Relevance Check",
                        relevant_suggestions > 0,
                        {
                            "total_suggestions": len(features),
                            "relevant_suggestions": relevant_suggestions,
                            "relevance_ratio": f"{relevant_suggestions}/{len(features)}"
                        }
                    )
            else:
                self.log_test("Autocomplete - Basic Query", False, {"error": "No features in response"})
            
            # Test autocomplete with bias
            bias_response = await autocomplete_locations(
                text="Central Park",
                limit=3,
                bias_location=(40.7831, -73.9712)  # NYC coordinates
            )
            
            self.log_test(
                "Autocomplete - With Bias",
                "features" in bias_response,
                {
                    "query": "Central Park",
                    "bias_location": "NYC",
                    "suggestions_count": len(bias_response.get("features", []))
                }
            )
            
        except Exception as e:
            self.log_test("Autocomplete API", False, {"error": str(e)})
    
    async def test_utility_functions(self):
        """Test utility functions"""
        try:
            print("\n🔧 Testing Utility Functions...")
            
            # Test coordinate validation
            coord_tests = [
                ((40.7580, -73.9855), True),   # Valid NYC coordinates
                ((91, 0), False),              # Invalid latitude
                ((0, 181), False),             # Invalid longitude
                ((-90, -180), True),           # Valid boundary coordinates
                ((90, 180), True),             # Valid boundary coordinates
            ]
            
            coord_results = []
            for (lat, lon), expected in coord_tests:
                result = validate_coordinates(lat, lon)
                coord_results.append(result == expected)
            
            self.log_test(
                "Coordinate Validation",
                all(coord_results),
                {
                    "test_cases": len(coord_tests),
                    "passed": sum(coord_results),
                    "all_passed": all(coord_results)
                }
            )
            
            # Test category validation
            test_categories = ["accommodation", "commercial", "invalid_category", "catering"]
            validated_categories = validate_place_categories(test_categories)
            
            self.log_test(
                "Category Validation",
                len(validated_categories) == 3,  # Should filter out invalid_category
                {
                    "input_categories": test_categories,
                    "validated_categories": validated_categories,
                    "filtered_count": len(validated_categories)
                }
            )
            
            # Test PlaceType enum
            place_types = [pt.value for pt in PlaceType]
            
            self.log_test(
                "Place Types Enum",
                len(place_types) > 5,
                {
                    "available_types": len(place_types),
                    "sample_types": place_types[:5]
                }
            )
            
        except Exception as e:
            self.log_test("Utility Functions", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid coordinates (validate before API call)
            try:
                # This should be caught by our validation
                if not validate_coordinates(999, 999):
                    raise ValueError("Invalid coordinates")
                await search_locations_by_coordinates(
                    lat=999,  # Invalid latitude
                    lon=999,  # Invalid longitude
                    radius=1000
                )
                self.log_test("Error Handling - Invalid Coordinates", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Coordinates", True, {"expected_error": str(e)})
            
            # Test invalid place_id
            try:
                await get_location_details("invalid_place_id_12345")
                self.log_test("Error Handling - Invalid Place ID", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Place ID", True, {"expected_error": str(e)})
            
            # Test empty search query
            try:
                await search_locations("")
                self.log_test("Error Handling - Empty Query", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Empty Query", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Geoapify Location API Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_location_search()
        await self.test_nearby_search()
        await self.test_location_details()
        await self.test_autocomplete()
        await self.test_utility_functions()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("geoapify_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: geoapify_test_results.json")

async def main():
    """Main test runner"""
    print("🗺️ Geoapify Location API Test Suite")
    print("Documentation: https://apidocs.geoapify.com/docs/places/#api")
    print("Documentation: https://apidocs.geoapify.com/docs/place-details/#place-details")
    print()
    
    # Check environment variables
    required_env_vars = ["GEOAPIFY_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('GEOAPIFY_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = GeoapifyAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())