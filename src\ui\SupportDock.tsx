"use client";
import { useState, useEffect, useCallback, useRef } from "react";
import { PlusCircle, History } from "lucide-react";
import PlannerWizard from "./PlannerWizard";
import HistoryPanel from "./HistoryPanel";
import { useToast, ToastContainer } from "./Toast";

export default function SupportDock() {
  const [plannerOpen, setPlannerOpen] = useState(false);
  const [historyPanelOpen, setHistoryPanelOpen] = useState(false);
  const [synTourPressed, setSynTourPressed] = useState(false);
  const [newChatPressed, setNewChatPressed] = useState(false);
  const [historyPressed, setHistoryPressed] = useState(false);
  const historyPanelRef = useRef<HTMLDivElement>(null);
  const historyButtonRef = useRef<HTMLButtonElement>(null);
  const { toasts, showToast, removeToast } = useToast();

  // 固定面板高度
  const panelHeight = 400;

  // 切换历史面板
  const toggleHistoryPanel = useCallback(() => {
    setHistoryPanelOpen(!historyPanelOpen);
    console.debug('ui.historyPanel.toggle', { open: !historyPanelOpen });
  }, [historyPanelOpen]);

  // 点击外部区域关闭面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (historyPanelOpen && 
          historyPanelRef.current && 
          historyButtonRef.current &&
          !historyPanelRef.current.contains(event.target as Node) &&
          !historyButtonRef.current.contains(event.target as Node)) {
        setHistoryPanelOpen(false);
        console.debug('ui.historyPanel.clickOutside');
      }
    };

    if (historyPanelOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [historyPanelOpen]);

  // 处理新聊天
  const handleNewChat = useCallback(() => {
    // 触发自定义事件来开始新对话
    window.dispatchEvent(new CustomEvent('syntour.newChat'));
    console.debug('ui.newChat.click');
    showToast({
      id: Date.now().toString(),
      type: 'success',
      message: 'Started new conversation!'
    });
  }, [showToast]);

  // 处理选择聊天会话
  const handleSelectChat = useCallback((chatId: string) => {
    // TODO: 实现切换到指定聊天会话的逻辑
    console.debug('ui.history.selectChat', { chatId });
    showToast({
      id: Date.now().toString(),
      type: 'success',
      message: `Loading chat session...`
    });
  }, [showToast]);

  // Planner相关事件监听
  useEffect(() => {
    function openViaEvent() { setPlannerOpen(true); }
    function closeViaEvent() { setPlannerOpen(false); }
    window.addEventListener("mytour.open_planner", openViaEvent);
    window.addEventListener("mytour.close_planner", closeViaEvent);
    return () => {
      window.removeEventListener("mytour.open_planner", openViaEvent);
      window.removeEventListener("mytour.close_planner", closeViaEvent);
    };
  }, []);

  function handlePlannerSubmit(answers: any) {
    // The PlannerWizard will automatically fill the chat input
    console.debug('ui.planner.submit', answers);
  }

  return (
    <>
      {/* 主悬浮按钮列 */}
      <div className="fixed left-4 bottom-28 flex flex-col gap-3 z-[70]">
        {/* SynTour icon - 增强交互效果 */}
        <button
          className={`w-16 h-16 rounded-2xl text-white shadow-soft overflow-hidden hover:scale-105 active:scale-95 transition-all duration-200 ${synTourPressed ? 'brightness-110 shadow-lg' : ''}`}
          aria-label="SynTour Planner"
          style={{ 
            background: synTourPressed 
              ? "linear-gradient(90deg, #0066FF, #FFD700)" 
              : "linear-gradient(90deg, #010066, #FFCC00)" 
          }}
          onMouseDown={() => setSynTourPressed(true)}
          onMouseUp={() => setSynTourPressed(false)}
          onMouseLeave={() => setSynTourPressed(false)}
          onClick={() => {
            window.dispatchEvent(new Event("mytour.open_planner"));
            setSynTourPressed(true);
            setTimeout(() => setSynTourPressed(false), 150);
          }}
          title="Open SynTour Planner"
        >
          <img
            src="/branding/syntour.png"
            onError={(e) => { 
              const target = e.currentTarget as HTMLImageElement;
              if (target.src !== "/branding/syntour-placeholder.svg") {
                target.src = "/branding/syntour-placeholder.svg";
              }
            }}
            alt="SynTour"
            className="w-full h-full object-cover"
          />
        </button>

        {/* New chat - 增强交互效果 */}
        <button
          className={`w-12 h-12 rounded-2xl bg-white border hover:bg-gray-50 hover:scale-105 active:scale-95 transition-all duration-200 
            flex items-center justify-center text-gray-600 hover:text-gray-800 shadow-soft
            ${newChatPressed ? 'bg-gray-100 shadow-lg' : ''}`}
          onClick={handleNewChat}
          onMouseDown={() => setNewChatPressed(true)}
          onMouseUp={() => setNewChatPressed(false)}
          onMouseLeave={() => setNewChatPressed(false)}
          aria-label="New chat"
          title="Start new chat"
        >
          <PlusCircle className="w-5 h-5" />
        </button>

        {/* History - 增强交互效果，关联面板 */}
        <button
          ref={historyButtonRef}
          className={`w-12 h-12 rounded-2xl border hover:scale-105 active:scale-95 transition-all duration-200 
            flex items-center justify-center shadow-soft
            ${historyPanelOpen 
              ? 'bg-gradient-to-r from-myrNavy to-myrYellow text-white border-transparent' 
              : 'bg-white text-gray-600 hover:text-gray-800 hover:bg-gray-50 border-gray-200'
            }
            ${historyPressed ? 'brightness-110 shadow-lg' : ''}`}
          onClick={toggleHistoryPanel}
          onMouseDown={() => setHistoryPressed(true)}
          onMouseUp={() => setHistoryPressed(false)}
          onMouseLeave={() => setHistoryPressed(false)}
          aria-label="Chat History"
          aria-expanded={historyPanelOpen}
          title={historyPanelOpen ? "Close chat history" : "Open chat history"}
        >
          <History className="w-5 h-5" />
        </button>
      </div>

      {/* 历史聊天面板 */}
      <div ref={historyPanelRef}>
        <HistoryPanel
          isOpen={historyPanelOpen}
          height={panelHeight}
          onSelectChat={handleSelectChat}
        />
      </div>

      {/* Planner弹窗 - 改为右侧固定面板 */}
      {plannerOpen && (
        <div className="fixed right-0 top-0 h-full w-1/4 bg-white shadow-2xl z-[9999] border-l border-gray-200 overflow-hidden">
          <div className="h-full flex flex-col">
            {/* 头部 */}
            <div className="flex-shrink-0 bg-white border-b p-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-my-primary">SynTour Planner</h2>
              <button 
                onClick={() => setPlannerOpen(false)}
                className="text-gray-400 hover:text-gray-600 text-2xl leading-none"
                aria-label="Close planner"
              >
                ×
              </button>
            </div>
            {/* 内容区域 */}
            <div className="flex-1 overflow-y-auto">
              <PlannerWizard onSubmit={handlePlannerSubmit} />
            </div>
          </div>
        </div>
      )}

      {/* Toast通知 */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </>
  );
}
