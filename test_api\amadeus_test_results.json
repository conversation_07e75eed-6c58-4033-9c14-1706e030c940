{"summary": {"total": 12, "passed": 11, "failed": 1, "success_rate": 91.66666666666666}, "results": [{"test": "Authentication - Token Acquisition", "success": true, "timestamp": "2025-08-30T12:10:32.143576", "details": {"token_length": 28}}, {"test": "Flight Offers - Basic Search", "success": true, "timestamp": "2025-08-30T12:10:34.908947", "details": {"offers_count": 5, "route": "BOS-CHI"}}, {"test": "Flight Offer Structure", "success": true, "timestamp": "2025-08-30T12:10:34.908947", "details": {"missing_fields": [], "available_fields": ["type", "id", "source", "instantTicketingRequired", "nonHomogeneous", "oneWay", "isUpsellOffer", "lastTicketingDate", "lastTicketingDateTime", "numberOfBookableSeats"], "price": "218.51"}}, {"test": "Flight Offers - Round Trip", "success": true, "timestamp": "2025-08-30T12:10:40.376484", "details": {"route": "NYC-LAX", "round_trip": true}}, {"test": "Flight Status - Valid Flight", "success": true, "timestamp": "2025-08-30T12:10:41.289965", "details": {"flights_count": 1, "flight": "AA100"}}, {"test": "Flight Status Structure", "success": true, "timestamp": "2025-08-30T12:10:41.291511", "details": {"available_fields": ["type", "scheduledDepartureDate", "flightDesignator", "flightPoints", "segments", "legs"], "flight_designator": {"carrierCode": "AA", "flightNumber": 100}}}, {"test": "Cheapest Dates API", "success": false, "timestamp": "2025-08-30T12:10:42.234209", "details": {"error": ""}}, {"test": "Airport Search - By City", "success": true, "timestamp": "2025-08-30T12:10:43.126289", "details": {"airports_count": 4, "keyword": "Boston"}}, {"test": "Airport Data Structure", "success": true, "timestamp": "2025-08-30T12:10:43.126289", "details": {"sample_airport": "BOS", "available_fields": ["type", "subType", "name", "detailedName", "id", "self", "timeZoneOffset", "iataCode", "geoCode", "address", "analytics"]}}, {"test": "Airport Search - By IATA Code", "success": true, "timestamp": "2025-08-30T12:10:44.080020", "details": {"keyword": "BOS"}}, {"test": "Error Handling - Invalid Airport", "success": true, "timestamp": "2025-08-30T12:10:44.968228", "details": {"expected_error": ""}}, {"test": "Error <PERSON> - Past Date", "success": true, "timestamp": "2025-08-30T12:10:45.942606", "details": {"expected_error": ""}}]}