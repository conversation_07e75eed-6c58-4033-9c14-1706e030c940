"use client";

function SafeImage({ src, alt, w=720, h=420, className="" }:{src:string;alt:string;w?:number;h?:number;className?:string}) {
  // Use a standard <img> fallback if local file missing
  return (
    <div className={`relative overflow-hidden rounded-2xl border shadow-soft ${className}`} style={{width: w, height: h}}>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover"
        onError={(e) => {
          // 如果图片加载失败，隐藏元素
          const target = e.currentTarget as HTMLImageElement;
          target.style.display = 'none';
        }}
      />
    </div>
  );
}

export default function MalaysiaShowcase(){
  return (
    <section className="container-page my-10">
      <h2 className="text-xl font-semibold mb-3">Explore Malaysia</h2>
      <div className="grid md:grid-cols-2 gap-5">
        <SafeImage src="/images/malaysia-1.jpg" alt="Malaysia scenery 1" />
        <SafeImage src="/images/malaysia-2.jpg" alt="Malaysia scenery 2" />
      </div>
      <p className="text-sm text-gray-600 mt-3">Tip: if images do not appear, ensure files exist under <code>/public/images</code> or replace with your own.</p>
    </section>
  );
}
