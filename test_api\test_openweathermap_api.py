#!/usr/bin/env python3
"""
OpenWeatherMap API Test Script

This script tests the OpenWeatherMap API integration using the updated
service layer and models. It demonstrates various API endpoints and validates
the response structure against our Pydantic models.

Documentation: https://openweathermap.org/current
API Documentation: https://openweathermap.org/api
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime, date, timedelta

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.openweathermap_service import (
    get_current_weather_by_city,
    get_current_weather_by_coordinates,
    get_current_weather_by_zip,
    get_coordinates_by_city,
    get_city_by_coordinates,
    get_5day_forecast,
    get_current_air_pollution,
    get_current_uv_index,
    get_travel_recommendation,
    get_weather_comparison,
    kelvin_to_celsius,
    celsius_to_fahrenheit,
    get_weather_icon_url,
    get_weather_severity_level
)
from app.models.openweathermap_model import (
    CurrentWeatherResponse,
    WeatherForecastResponse,
    Units,
    Language
)

class OpenWeatherMapAPITester:
    """Test class for OpenWeatherMap API endpoints"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_current_weather_by_city(self):
        """Test current weather by city name endpoint"""
        try:
            print("\n🌤️ Testing Current Weather by City...")
            
            # Test basic city weather (London)
            response = await get_current_weather_by_city(
                city_name="London",
                country_code="UK",
                units="metric"
            )
            
            # Validate response structure
            if "main" in response and "weather" in response:
                temp = response["main"].get("temp")
                condition = response["weather"][0].get("main") if response["weather"] else None
                
                self.log_test(
                    "Current Weather - London",
                    True,
                    {
                        "city": response.get("name"),
                        "country": response.get("sys", {}).get("country"),
                        "temperature": temp,
                        "condition": condition,
                        "units": "metric"
                    }
                )
                
                # Test required fields
                required_fields = ["coord", "weather", "main", "sys", "name"]
                missing_fields = [field for field in required_fields if field not in response]
                
                self.log_test(
                    "Weather Response Structure",
                    len(missing_fields) == 0,
                    {
                        "missing_fields": missing_fields,
                        "available_fields": list(response.keys())[:10]
                    }
                )
            else:
                self.log_test("Current Weather - London", False, {"error": "Missing main or weather data"})
            
            # Test with different units (imperial)
            imperial_response = await get_current_weather_by_city(
                city_name="New York",
                state_code="NY",
                country_code="US",
                units="imperial"
            )
            
            self.log_test(
                "Current Weather - Imperial Units",
                "main" in imperial_response,
                {
                    "city": imperial_response.get("name"),
                    "temperature_f": imperial_response.get("main", {}).get("temp"),
                    "units": "imperial"
                }
            )
            
            # Test with different language
            spanish_response = await get_current_weather_by_city(
                city_name="Madrid",
                country_code="ES",
                units="metric",
                lang="es"
            )
            
            self.log_test(
                "Current Weather - Spanish Language",
                "weather" in spanish_response,
                {
                    "city": spanish_response.get("name"),
                    "description": spanish_response.get("weather", [{}])[0].get("description"),
                    "language": "es"
                }
            )
            
        except Exception as e:
            self.log_test("Current Weather by City API", False, {"error": str(e)})
    
    async def test_current_weather_by_coordinates(self):
        """Test current weather by coordinates endpoint"""
        try:
            print("\n🗺️ Testing Current Weather by Coordinates...")
            
            # Test weather for Paris coordinates
            response = await get_current_weather_by_coordinates(
                lat=48.8566,
                lon=2.3522,
                units="metric"
            )
            
            if "main" in response and "coord" in response:
                coords = response["coord"]
                temp = response["main"].get("temp")
                
                self.log_test(
                    "Current Weather - Coordinates (Paris)",
                    True,
                    {
                        "city": response.get("name"),
                        "latitude": coords.get("lat"),
                        "longitude": coords.get("lon"),
                        "temperature": temp
                    }
                )
                
                # Test coordinate accuracy
                lat_diff = abs(coords.get("lat", 0) - 48.8566)
                lon_diff = abs(coords.get("lon", 0) - 2.3522)
                
                self.log_test(
                    "Coordinate Accuracy",
                    lat_diff < 0.1 and lon_diff < 0.1,
                    {
                        "requested_lat": 48.8566,
                        "returned_lat": coords.get("lat"),
                        "requested_lon": 2.3522,
                        "returned_lon": coords.get("lon"),
                        "lat_diff": lat_diff,
                        "lon_diff": lon_diff
                    }
                )
            else:
                self.log_test("Current Weather - Coordinates (Paris)", False, {"error": "Missing main or coord data"})
            
            # Test extreme coordinates (Tokyo)
            tokyo_response = await get_current_weather_by_coordinates(
                lat=35.6762,
                lon=139.6503,
                units="metric"
            )
            
            self.log_test(
                "Current Weather - Tokyo Coordinates",
                "main" in tokyo_response,
                {
                    "city": tokyo_response.get("name"),
                    "timezone": tokyo_response.get("timezone")
                }
            )
            
        except Exception as e:
            self.log_test("Current Weather by Coordinates API", False, {"error": str(e)})
    
    async def test_current_weather_by_zip(self):
        """Test current weather by ZIP code endpoint"""
        try:
            print("\n📮 Testing Current Weather by ZIP Code...")
            
            # Test US ZIP code
            response = await get_current_weather_by_zip(
                zip_code="10001",
                country_code="US",
                units="metric"
            )
            
            if "main" in response:
                self.log_test(
                    "Current Weather - US ZIP Code",
                    True,
                    {
                        "city": response.get("name"),
                        "country": response.get("sys", {}).get("country"),
                        "zip_area": "10001, US",
                        "temperature": response.get("main", {}).get("temp")
                    }
                )
            else:
                self.log_test("Current Weather - US ZIP Code", False, {"error": "Missing main data"})
            
            # Test UK postal code
            try:
                uk_response = await get_current_weather_by_zip(
                    zip_code="SW1A 1AA",
                    country_code="GB",
                    units="metric"
                )
                
                self.log_test(
                    "Current Weather - UK Postal Code",
                    "main" in uk_response,
                    {
                        "city": uk_response.get("name"),
                        "postal_code": "SW1A 1AA, GB"
                    }
                )
            except Exception as e:
                # UK postal codes might not work with OpenWeatherMap
                self.log_test("Current Weather - UK Postal Code", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Current Weather by ZIP Code API", False, {"error": str(e)})
    
    async def test_geocoding_apis(self):
        """Test geocoding and reverse geocoding endpoints"""
        try:
            print("\n🌍 Testing Geocoding APIs...")
            
            # Test direct geocoding (city to coordinates)
            geocode_response = await get_coordinates_by_city(
                city_name="Berlin",
                country_code="DE",
                limit=1
            )
            
            if geocode_response and len(geocode_response) > 0:
                location = geocode_response[0]
                lat = location.get("lat")
                lon = location.get("lon")
                
                self.log_test(
                    "Geocoding - City to Coordinates",
                    lat is not None and lon is not None,
                    {
                        "city": location.get("name"),
                        "country": location.get("country"),
                        "latitude": lat,
                        "longitude": lon
                    }
                )
                
                # Test reverse geocoding (coordinates to city)
                if lat and lon:
                    reverse_response = await get_city_by_coordinates(
                        lat=lat,
                        lon=lon,
                        limit=1
                    )
                    
                    if reverse_response and len(reverse_response) > 0:
                        reverse_location = reverse_response[0]
                        
                        self.log_test(
                            "Reverse Geocoding - Coordinates to City",
                            True,
                            {
                                "original_city": location.get("name"),
                                "reverse_city": reverse_location.get("name"),
                                "country": reverse_location.get("country")
                            }
                        )
                    else:
                        self.log_test("Reverse Geocoding - Coordinates to City", False, {"error": "No reverse geocoding results"})
            else:
                self.log_test("Geocoding - City to Coordinates", False, {"error": "No geocoding results"})
            
        except Exception as e:
            self.log_test("Geocoding APIs", False, {"error": str(e)})
    
    async def test_weather_forecast(self):
        """Test 5-day weather forecast endpoint"""
        try:
            print("\n📅 Testing Weather Forecast...")
            
            # Test 5-day forecast for London
            forecast_response = await get_5day_forecast(
                city_name="London",
                country_code="UK",
                units="metric",
                cnt=10  # Get 10 forecast entries
            )
            
            if "list" in forecast_response and "city" in forecast_response:
                forecast_list = forecast_response["list"]
                city_info = forecast_response["city"]
                
                self.log_test(
                    "Weather Forecast - 5 Day",
                    len(forecast_list) > 0,
                    {
                        "city": city_info.get("name"),
                        "country": city_info.get("country"),
                        "forecast_entries": len(forecast_list),
                        "requested_count": 10
                    }
                )
                
                # Test forecast entry structure
                if forecast_list:
                    first_entry = forecast_list[0]
                    required_fields = ["dt", "main", "weather", "dt_txt"]
                    missing_fields = [field for field in required_fields if field not in first_entry]
                    
                    self.log_test(
                        "Forecast Entry Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields,
                            "forecast_time": first_entry.get("dt_txt"),
                            "temperature": first_entry.get("main", {}).get("temp")
                        }
                    )
            else:
                self.log_test("Weather Forecast - 5 Day", False, {"error": "Missing list or city data"})
            
            # Test forecast by coordinates
            coord_forecast = await get_5day_forecast(
                lat=40.7128,
                lon=-74.0060,  # New York coordinates
                units="imperial",
                cnt=5
            )
            
            self.log_test(
                "Weather Forecast - By Coordinates",
                "list" in coord_forecast,
                {
                    "location": "New York (coordinates)",
                    "units": "imperial",
                    "entries": len(coord_forecast.get("list", []))
                }
            )
            
        except Exception as e:
            self.log_test("Weather Forecast API", False, {"error": str(e)})
    
    async def test_air_pollution_api(self):
        """Test air pollution endpoint"""
        try:
            print("\n🏭 Testing Air Pollution API...")
            
            # Test current air pollution for London
            air_response = await get_current_air_pollution(
                lat=51.5074,
                lon=-0.1278
            )
            
            if "list" in air_response:
                air_data = air_response["list"]
                if air_data and len(air_data) > 0:
                    current_air = air_data[0]
                    main_air = current_air.get("main", {})
                    components = current_air.get("components", {})
                    
                    self.log_test(
                        "Air Pollution - Current Data",
                        "aqi" in main_air,
                        {
                            "location": "London",
                            "aqi": main_air.get("aqi"),
                            "co": components.get("co"),
                            "pm2_5": components.get("pm2_5"),
                            "pm10": components.get("pm10")
                        }
                    )
                    
                    # Test air quality components
                    expected_components = ["co", "no", "no2", "o3", "so2", "pm2_5", "pm10", "nh3"]
                    available_components = [comp for comp in expected_components if comp in components]
                    
                    self.log_test(
                        "Air Pollution - Components",
                        len(available_components) >= 5,  # At least 5 components should be available
                        {
                            "available_components": available_components,
                            "total_available": len(available_components)
                        }
                    )
                else:
                    self.log_test("Air Pollution - Current Data", False, {"error": "No air pollution data in list"})
            else:
                self.log_test("Air Pollution - Current Data", False, {"error": "No list data in response"})
            
        except Exception as e:
            self.log_test("Air Pollution API", False, {"error": str(e)})
    
    async def test_uv_index_api(self):
        """Test UV index endpoint"""
        try:
            print("\n☀️ Testing UV Index API...")
            
            # Test current UV index for Sydney (sunny location)
            uv_response = await get_current_uv_index(
                lat=-33.8688,
                lon=151.2093
            )
            
            if "value" in uv_response:
                uv_value = uv_response["value"]
                
                self.log_test(
                    "UV Index - Current Data",
                    isinstance(uv_value, (int, float)),
                    {
                        "location": "Sydney",
                        "uv_index": uv_value,
                        "date": uv_response.get("date_iso"),
                        "coordinates": f"{uv_response.get('lat')}, {uv_response.get('lon')}"
                    }
                )
                
                # Test UV index range (should be 0-11+)
                self.log_test(
                    "UV Index - Valid Range",
                    0 <= uv_value <= 15,  # Allow some margin for extreme values
                    {
                        "uv_value": uv_value,
                        "valid_range": "0-15"
                    }
                )
            else:
                self.log_test("UV Index - Current Data", False, {"error": "No UV value in response"})
            
        except Exception as e:
            self.log_test("UV Index API", False, {"error": str(e)})
    
    async def test_utility_functions(self):
        """Test utility functions"""
        try:
            print("\n🔧 Testing Utility Functions...")
            
            # Test temperature conversions
            kelvin_temp = 293.15  # 20°C
            celsius_result = kelvin_to_celsius(kelvin_temp)
            fahrenheit_result = celsius_to_fahrenheit(celsius_result)
            
            self.log_test(
                "Temperature Conversions",
                abs(celsius_result - 20.0) < 0.1 and abs(fahrenheit_result - 68.0) < 0.1,
                {
                    "kelvin": kelvin_temp,
                    "celsius": celsius_result,
                    "fahrenheit": fahrenheit_result
                }
            )
            
            # Test weather icon URL generation
            icon_url = get_weather_icon_url("01d", "2x")
            expected_url = "https://openweathermap.org/img/wn/<EMAIL>"
            
            self.log_test(
                "Weather Icon URL",
                icon_url == expected_url,
                {
                    "generated_url": icon_url,
                    "expected_url": expected_url
                }
            )
            
            # Test weather severity levels
            severity_tests = [
                (800, "none"),      # Clear sky
                (801, "light"),     # Few clouds
                (500, "light"),     # Light rain
                (502, "moderate"),  # Heavy rain
                (200, "severe"),    # Thunderstorm
                (781, "severe")     # Tornado
            ]
            
            severity_results = []
            for weather_id, expected_severity in severity_tests:
                actual_severity = get_weather_severity_level(weather_id)
                severity_results.append(actual_severity == expected_severity)
            
            self.log_test(
                "Weather Severity Levels",
                all(severity_results),
                {
                    "test_cases": len(severity_tests),
                    "passed": sum(severity_results),
                    "severity_tests": dict(severity_tests)
                }
            )
            
        except Exception as e:
            self.log_test("Utility Functions", False, {"error": str(e)})
    
    async def test_travel_recommendations(self):
        """Test travel recommendation functionality"""
        try:
            print("\n🧳 Testing Travel Recommendations...")
            
            # Get weather data for testing
            weather_data = await get_current_weather_by_city(
                city_name="Barcelona",
                country_code="ES",
                units="metric"
            )
            
            # Generate travel recommendations
            recommendation = get_travel_recommendation(weather_data)
            
            required_keys = ["score", "recommendation_text", "warnings", "activities", "clothing", "tips"]
            missing_keys = [key for key in required_keys if key not in recommendation]
            
            self.log_test(
                "Travel Recommendations - Structure",
                len(missing_keys) == 0,
                {
                    "missing_keys": missing_keys,
                    "recommendation_score": recommendation.get("score"),
                    "activities_count": len(recommendation.get("activities", [])),
                    "warnings_count": len(recommendation.get("warnings", []))
                }
            )
            
            # Test recommendation score range
            score = recommendation.get("score", -1)
            self.log_test(
                "Travel Recommendations - Score Range",
                0 <= score <= 10,
                {
                    "score": score,
                    "valid_range": "0-10",
                    "recommendation_text": recommendation.get("recommendation_text", "")
                }
            )
            
        except Exception as e:
            self.log_test("Travel Recommendations", False, {"error": str(e)})
    
    async def test_weather_comparison(self):
        """Test weather comparison functionality"""
        try:
            print("\n⚖️ Testing Weather Comparison...")
            
            # Test weather comparison between multiple cities
            locations = [
                {"name": "London", "city": "London", "country": "UK"},
                {"name": "Paris", "city": "Paris", "country": "FR"},
                {"name": "Berlin", "city": "Berlin", "country": "DE"}
            ]
            
            comparison = await get_weather_comparison(locations)
            
            if "locations" in comparison and "analysis" in comparison:
                locations_data = comparison["locations"]
                analysis = comparison["analysis"]
                
                self.log_test(
                    "Weather Comparison - Multiple Cities",
                    len(locations_data) >= 2,  # At least 2 cities should work
                    {
                        "requested_locations": len(locations),
                        "successful_locations": len(locations_data),
                        "warmest_location": analysis.get("warmest_location"),
                        "coldest_location": analysis.get("coldest_location")
                    }
                )
                
                # Test analysis completeness
                analysis_keys = ["warmest_location", "coldest_location", "most_humid_location", "windiest_location"]
                available_analysis = [key for key in analysis_keys if analysis.get(key) is not None]
                
                self.log_test(
                    "Weather Comparison - Analysis",
                    len(available_analysis) >= 2,
                    {
                        "available_analysis": available_analysis,
                        "analysis_completeness": f"{len(available_analysis)}/{len(analysis_keys)}"
                    }
                )
            else:
                self.log_test("Weather Comparison - Multiple Cities", False, {"error": "Missing locations or analysis data"})
            
        except Exception as e:
            self.log_test("Weather Comparison", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid city name
            try:
                await get_current_weather_by_city(
                    city_name="NonExistentCity12345",
                    country_code="XX"
                )
                self.log_test("Error Handling - Invalid City", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid City", True, {"expected_error": str(e)})
            
            # Test invalid coordinates
            try:
                await get_current_weather_by_coordinates(
                    lat=999,  # Invalid latitude
                    lon=999   # Invalid longitude
                )
                self.log_test("Error Handling - Invalid Coordinates", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Coordinates", True, {"expected_error": str(e)})
            
            # Test invalid ZIP code
            try:
                await get_current_weather_by_zip(
                    zip_code="00000",
                    country_code="XX"
                )
                self.log_test("Error Handling - Invalid ZIP", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid ZIP", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting OpenWeatherMap API Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_current_weather_by_city()
        await self.test_current_weather_by_coordinates()
        await self.test_current_weather_by_zip()
        await self.test_geocoding_apis()
        await self.test_weather_forecast()
        await self.test_air_pollution_api()
        await self.test_uv_index_api()
        await self.test_utility_functions()
        await self.test_travel_recommendations()
        await self.test_weather_comparison()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("openweathermap_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: openweathermap_test_results.json")

async def main():
    """Main test runner"""
    print("🌤️ OpenWeatherMap API Test Suite")
    print("Documentation: https://openweathermap.org/current")
    print("API Documentation: https://openweathermap.org/api")
    print()
    
    # Check environment variables
    required_env_vars = ["OPEN_WEATHER_MAP_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('OPEN_WEATHER_MAP_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = OpenWeatherMapAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())
