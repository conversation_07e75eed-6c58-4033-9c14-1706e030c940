"use client";
import { useState, useEffect } from "react";
import { Trash2, Clock, MessageCircle } from "lucide-react";
import { loadChatSessions, deleteChatSession, clearAllChatSessions, type ChatSession } from "@/utils/chatHistory";

interface HistoryPanelProps {
  isOpen: boolean;
  height: number;
  onSelectChat: (chatId: string) => void;
}

export default function HistoryPanel({ isOpen, height, onSelectChat }: HistoryPanelProps) {
  const [chatHistory, setChatHistory] = useState<ChatSession[]>([]);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 从localStorage加载历史记录
  useEffect(() => {
    const loadHistory = () => {
      try {
        const sessions = loadChatSessions();
        setChatHistory(sessions);
      } catch (error) {
        console.error('Failed to load chat history:', error);
        setChatHistory([]);
      }
    };

    if (isOpen) {
      loadHistory();
      
      // 设置定时器定期刷新历史记录
      const interval = setInterval(loadHistory, 2000);
      return () => clearInterval(interval);
    }
  }, [isOpen]);

  // 选择聊天会话
  const handleSelectChat = (chat: ChatSession) => {
    setSelectedChatId(chat.id);
    // 触发自定义事件来加载聊天会话
    window.dispatchEvent(new CustomEvent('syntour.loadChatSession', { 
      detail: { sessionId: chat.id } 
    }));
    console.debug('ui.history.openChat', { chatId: chat.id, title: chat.title });
  };

  // 清空历史记录
  const handleClearHistory = () => {
    if (showClearConfirm) {
      clearAllChatSessions();
      setChatHistory([]);
      setSelectedChatId(null);
      setShowClearConfirm(false);
      console.debug('ui.history.clearAll');
    } else {
      setShowClearConfirm(true);
      setTimeout(() => setShowClearConfirm(false), 3000);
    }
  };

  // 删除单个会话
  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    deleteChatSession(chatId);
    const updatedChats = chatHistory.filter(chat => chat.id !== chatId);
    setChatHistory(updatedChats);
    if (selectedChatId === chatId) {
      setSelectedChatId(null);
    }
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed left-4 bg-white/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 z-[70] transition-all duration-300 ${
        isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-full pointer-events-none'
      }`}
      style={{ 
        width: '280px',
        height: `${height}px`,
        bottom: '140px' // 在第三个按钮上方
      }}
      aria-expanded={isOpen}
    >
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200/50">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-4 h-4 text-myrNavy" />
          <h3 className="font-semibold text-gray-900 text-sm">Chat History</h3>
        </div>
        <button
          onClick={handleClearHistory}
          className={`text-xs px-2 py-1 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-300 ${
            showClearConfirm
              ? 'bg-red-500 text-white hover:bg-red-600'
              : 'text-gray-500 hover:text-red-500 hover:bg-red-50'
          }`}
          title={showClearConfirm ? 'Click again to confirm' : 'Clear all chat history'}
        >
          {showClearConfirm ? 'Confirm?' : 'Clear'}
        </button>
      </div>

      {/* 聊天列表 */}
      <div className="flex-1 overflow-y-auto p-2" style={{ height: height - 60 }}>
        {chatHistory.length === 0 ? (
          /* 空状态 */
          <div className="flex flex-col items-center justify-center h-full text-center p-4">
            <MessageCircle className="w-12 h-12 text-gray-300 mb-3" />
            <p className="text-sm text-gray-500 mb-1">暂无会话</p>
            <p className="text-xs text-gray-400">开始一次新的对话吧</p>
          </div>
        ) : (
          /* 聊天会话列表 */
          <div className="space-y-1">
            {chatHistory.map((chat) => (
              <div
                key={chat.id}
                onClick={() => handleSelectChat(chat)}
                className={`p-3 rounded-xl cursor-pointer transition-all duration-200 group hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-myrNavy/50 ${
                  selectedChatId === chat.id
                    ? 'bg-myrYellow/10 border border-myrNavy ring-1 ring-myrNavy/30'
                    : 'hover:shadow-sm'
                }`}
                tabIndex={0}
                role="button"
                aria-label={`Open chat: ${chat.title}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleSelectChat(chat);
                  }
                }}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 truncate mb-1">
                      {chat.title}
                    </h4>
                    <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                      {chat.lastMessage}
                    </p>
                    <div className="flex items-center gap-3 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatTime(chat.createdAt)}
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3" />
                        {chat.messageCount}
                      </div>
                    </div>
                  </div>
                  
                  {/* 删除按钮 */}
                  <button
                    onClick={(e) => handleDeleteChat(chat.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-300"
                    aria-label="Delete chat"
                    title="Delete this chat"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
