// Enhanced AI Service for FastAPI backend with multimodal support

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

export interface ChatRequest {
  message: string;
  context?: string;
  user_preferences?: {
    duration?: string;
    budget?: string;
    interests?: string;
    travelers?: string;
  };
}

export interface ChatResponse {
  success: boolean;
  response?: string;
  error?: string;
  model?: string;
  processing_info?: {
    prompt_type?: string;
    media_types?: string[];
    files_processed?: number;
    transcribed_text?: string;
    audio_duration?: number;
  };
  is_truncated?: boolean;
  continuation_id?: string;
}

export interface FileUploadResponse {
  success: boolean;
  file_info?: {
    type: string;
    category: string;
    supported: boolean;
  };
  processed_content?: string;
  error?: string;
}

export class AIService {
  private static readonly CHAT_ENDPOINT = `${API_BASE_URL}/api/ai/chat`;
  private static readonly MULTIMODAL_ENDPOINT = `${API_BASE_URL}/api/ai/multimodal`;
  private static readonly TRAVEL_ENDPOINT = `${API_BASE_URL}/api/travel/plan`;
  private static readonly VOICE_ENDPOINT = `${API_BASE_URL}/api/ai/voice`;
  private static readonly UPLOAD_ENDPOINT = `${API_BASE_URL}/api/upload`;
  private static readonly CONTINUE_ENDPOINT = `${API_BASE_URL}/api/ai/continue`;

  // Basic text chat
  static async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      // 验证请求数据
      if (!request.message || request.message.trim() === '') {
        return {
          success: false,
          error: 'Message cannot be empty'
        };
      }

      const response = await fetch(this.CHAT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: request.message.trim(),
          context: request.context || '',
          user_preferences: request.user_preferences || {}
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('AI Service Error:', error);
      return {
        success: false,
        error: `Failed to connect to AI service: ${error.message}`,
      };
    }
  }

  // Multimodal chat with file uploads
  static async multimodalChat(
    message: string, 
    files: File[] = [], 
    context?: string
  ): Promise<ChatResponse> {
    try {
      const formData = new FormData();
      formData.append('message', message);
      
      if (context) {
        formData.append('context', context);
      }

      // Add files
      files.forEach((file, index) => {
        formData.append('files', file);
      });

      const response = await fetch(this.MULTIMODAL_ENDPOINT, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('Multimodal AI Error:', error);
      return {
        success: false,
        error: `Failed to process multimodal request: ${error.message}`,
      };
    }
  }

  // Travel planning with preferences
  static async planTravel(request: ChatRequest): Promise<ChatResponse> {
    try {
      const response = await fetch(this.TRAVEL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('Travel Planning Error:', error);
      return {
        success: false,
        error: `Failed to create travel plan: ${error.message}`,
      };
    }
  }

  // Voice chat with audio file
  static async voiceChat(audioFile: File): Promise<ChatResponse> {
    try {
      const formData = new FormData();
      formData.append('audio_file', audioFile);

      const response = await fetch(this.VOICE_ENDPOINT, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('Voice Chat Error:', error);
      return {
        success: false,
        error: `Failed to process voice request: ${error.message}`,
      };
    }
  }

  // File upload for preprocessing
  static async uploadFile(file: File): Promise<FileUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(this.UPLOAD_ENDPOINT, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('File Upload Error:', error);
      return {
        success: false,
        error: `Failed to upload file: ${error.message}`,
      };
    }
  }

  // Health check
  static async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      return response.ok;
    } catch {
      return false;
    }
  }

  // Get service status
  static async getServiceStatus(): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/`);
      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch {
      return null;
    }
  }

  // Helper function to determine file type
  static getFileType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop() || '';
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
      return 'image';
    }
    if (['mp3', 'wav', 'm4a', 'flac', 'ogg'].includes(ext)) {
      return 'audio';
    }
    if (['txt', 'pdf', 'doc', 'docx'].includes(ext)) {
      return 'document';
    }
    return 'unknown';
  }

  // Continue truncated response
  static async continueResponse(continuationId: string): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.CONTINUE_ENDPOINT}/${continuationId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error: any) {
      console.error('Continuation Error:', error);
      return {
        success: false,
        error: `Failed to continue response: ${error.message}`,
      };
    }
  }

  // Helper function to format file size
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}