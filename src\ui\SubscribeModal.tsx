"use client";
import { useState } from "react";
import { X, Mail, Check, FileText } from "lucide-react";

interface SubscribeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SubscribeModal({ isOpen, onClose }: SubscribeModalProps) {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError("Please enter your email address");
      return;
    }

    if (!email.includes("@") || !email.includes(".")) {
      setError("Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);
    setError("");

    // 模拟API调用
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      
      // 3秒后自动关闭
      setTimeout(() => {
        onClose();
        setIsSuccess(false);
        setEmail("");
      }, 3000);
    }, 1500);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setEmail("");
      setError("");
      setIsSuccess(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[100] p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-myrNavy via-my-blue-600 to-myrYellow text-white p-6 relative">
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="absolute top-4 right-4 text-white/70 hover:text-white hover:bg-white/20 rounded-lg p-1 transition-colors disabled:opacity-50"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <FileText className="w-6 h-6" />
            </div>
            <div>
              <h2 className="text-xl font-bold">Free Malaysia Travel Guide</h2>
              <p className="text-white/80 text-sm">Your ultimate travel companion</p>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-3 text-xs">
            <div className="text-center">
              <div className="font-semibold">🏝️ Islands</div>
              <div className="text-white/70">Langkawi, Redang</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">🍜 Food</div>
              <div className="text-white/70">Nasi Lemak, Satay</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">🏙️ Cities</div>
              <div className="text-white/70">KL, Penang</div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {!isSuccess ? (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Get Your Free Guide
                </h3>
                <p className="text-gray-600 text-sm">
                  Discover hidden gems, local favorites, and insider tips from Malaysia travel experts. 
                  Our comprehensive guide includes:
                </p>
                
                <ul className="mt-3 space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 rounded-full flex items-center justify-center">
                      <Check className="w-2.5 h-2.5 text-green-600" />
                    </div>
                    Best time to visit each destination
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 rounded-full flex items-center justify-center">
                      <Check className="w-2.5 h-2.5 text-green-600" />
                    </div>
                    Local food recommendations & prices
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 rounded-full flex items-center justify-center">
                      <Check className="w-2.5 h-2.5 text-green-600" />
                    </div>
                    Transportation & budget planning
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 rounded-full flex items-center justify-center">
                      <Check className="w-2.5 h-2.5 text-green-600" />
                    </div>
                    Cultural etiquette & useful phrases
                  </li>
                </ul>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      disabled={isSubmitting}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-my-blue-500 focus:border-transparent transition-colors disabled:opacity-50"
                      required
                    />
                  </div>
                  {error && (
                    <p className="mt-2 text-sm text-red-600">{error}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-myrNavy to-myrYellow text-white py-3 px-6 rounded-xl font-semibold hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      Sending Guide...
                    </div>
                  ) : (
                    "✨ Get Free Malaysia Guide"
                  )}
                </button>
              </form>

              <p className="mt-4 text-xs text-gray-500 text-center">
                No spam, just amazing travel tips. Unsubscribe anytime.
              </p>
            </>
          ) : (
            /* Success State */
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Thanks! Guide will be sent to your inbox
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Check your email for your free Malaysia travel guide. 
                Don&apos;t forget to check your spam folder too!
              </p>
              <div className="bg-gradient-to-r from-my-blue-50 to-my-yellow-50 rounded-xl p-4">
                <p className="text-sm text-gray-700">
                  <strong>What&apos;s next?</strong> Start planning your Malaysia adventure with SynTour AI!
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
