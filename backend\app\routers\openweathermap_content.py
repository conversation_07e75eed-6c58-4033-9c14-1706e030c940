# app/routers/openweathermap_content.py
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.services.openweathermap_service import (
    get_current_weather_by_city,
    get_current_weather_by_coordinates,
    get_current_weather_by_city_id,
    get_current_weather_by_zip,
    get_coordinates_by_city,
    get_city_by_coordinates,
    get_5day_forecast,
    get_current_air_pollution,
    get_air_pollution_forecast,
    get_current_uv_index,
    get_uv_index_forecast,
    get_travel_recommendation,
    get_weather_comparison,
    get_weather_icon_url
)
from app.models.openweathermap_model import (
    CurrentWeatherResponse,
    WeatherForecastResponse,
    GeocodeLocation,
    AirQuality,
    UVIndex,
    Units,
    Language,
    TravelWeatherRecommendation,
    WeatherComparison,
    OpenWeatherMapErrorResponse
)

router = APIRouter(prefix="/weather", tags=["OpenWeatherMap Weather API"])

@router.get("/current/city", response_model=CurrentWeatherResponse)
async def get_current_weather_city(
    q: str = Query(..., description="City name, state code and country code divided by comma (e.g., 'London', 'London,UK', 'New York,NY,US')"),
    units: Units = Query(Units.METRIC, description="Units of measurement"),
    lang: Language = Query(Language.ENGLISH, description="Language for weather descriptions")
):
    """
    Get current weather data by city name.
    
    You can call by city name or city name, state code and country code.
    Please note that searching by states available only for the USA locations.
    """
    # Parse city query
    parts = [part.strip() for part in q.split(",")]
    city_name = parts[0]
    state_code = parts[1] if len(parts) > 1 else None
    country_code = parts[2] if len(parts) > 2 else None
    
    if not city_name:
        raise HTTPException(status_code=400, detail="City name cannot be empty")
    
    return await get_current_weather_by_city(
        city_name=city_name,
        state_code=state_code,
        country_code=country_code,
        units=units.value,
        lang=lang.value
    )

@router.get("/current/coordinates", response_model=CurrentWeatherResponse)
async def get_current_weather_coordinates(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180),
    units: Units = Query(Units.METRIC, description="Units of measurement"),
    lang: Language = Query(Language.ENGLISH, description="Language for weather descriptions")
):
    """
    Get current weather data by geographic coordinates.
    
    Latitude and longitude are required parameters.
    """
    return await get_current_weather_by_coordinates(
        lat=lat,
        lon=lon,
        units=units.value,
        lang=lang.value
    )

@router.get("/current/city-id", response_model=CurrentWeatherResponse)
async def get_current_weather_city_id(
    id: int = Query(..., description="City ID", gt=0),
    units: Units = Query(Units.METRIC, description="Units of measurement"),
    lang: Language = Query(Language.ENGLISH, description="Language for weather descriptions")
):
    """
    Get current weather data by city ID.
    
    You can find city ID in city.list.json.gz file or by calling geocoding API.
    """
    return await get_current_weather_by_city_id(
        city_id=id,
        units=units.value,
        lang=lang.value
    )

@router.get("/current/zip", response_model=CurrentWeatherResponse)
async def get_current_weather_zip(
    zip: str = Query(..., description="ZIP code and country code divided by comma (e.g., '94040,US')"),
    units: Units = Query(Units.METRIC, description="Units of measurement"),
    lang: Language = Query(Language.ENGLISH, description="Language for weather descriptions")
):
    """
    Get current weather data by ZIP code.
    
    Please note that if country is not specified then the search works for USA as a default.
    """
    # Parse ZIP query
    parts = [part.strip() for part in zip.split(",")]
    zip_code = parts[0]
    country_code = parts[1] if len(parts) > 1 else None
    
    if not zip_code:
        raise HTTPException(status_code=400, detail="ZIP code cannot be empty")
    
    return await get_current_weather_by_zip(
        zip_code=zip_code,
        country_code=country_code,
        units=units.value,
        lang=lang.value
    )

@router.get("/forecast/5day", response_model=WeatherForecastResponse)
async def get_weather_forecast(
    lat: Optional[float] = Query(None, description="Latitude", ge=-90, le=90),
    lon: Optional[float] = Query(None, description="Longitude", ge=-180, le=180),
    q: Optional[str] = Query(None, description="City name, state code and country code divided by comma"),
    id: Optional[int] = Query(None, description="City ID", gt=0),
    zip: Optional[str] = Query(None, description="ZIP code and country code divided by comma"),
    units: Units = Query(Units.METRIC, description="Units of measurement"),
    lang: Language = Query(Language.ENGLISH, description="Language for weather descriptions"),
    cnt: Optional[int] = Query(None, description="Number of timestamps (max 40)", ge=1, le=40)
):
    """
    Get 5 day weather forecast with data every 3 hours.
    
    You can search weather forecast for 5 days with data every 3 hours by city name,
    city coordinates, city ID, or ZIP code.
    """
    # Validate that at least one location parameter is provided
    location_params = [lat and lon, q, id, zip]
    if not any(location_params):
        raise HTTPException(
            status_code=400, 
            detail="Must provide either coordinates (lat, lon), city name (q), city ID (id), or ZIP code (zip)"
        )
    
    # Validate coordinates if provided
    if lat is not None and lon is None:
        raise HTTPException(status_code=400, detail="Longitude is required when latitude is provided")
    if lon is not None and lat is None:
        raise HTTPException(status_code=400, detail="Latitude is required when longitude is provided")
    
    # Parse city query if provided
    city_name = None
    country_code = None
    if q:
        parts = [part.strip() for part in q.split(",")]
        city_name = parts[0]
        country_code = parts[1] if len(parts) > 1 else None
    
    # Parse ZIP query if provided
    zip_code = None
    zip_country = None
    if zip:
        parts = [part.strip() for part in zip.split(",")]
        zip_code = parts[0]
        zip_country = parts[1] if len(parts) > 1 else None
    
    return await get_5day_forecast(
        lat=lat,
        lon=lon,
        city_name=city_name,
        city_id=id,
        zip_code=zip_code,
        country_code=country_code or zip_country,
        units=units.value,
        lang=lang.value,
        cnt=cnt
    )

@router.get("/geocoding/direct")
async def geocode_city(
    q: str = Query(..., description="City name, state code and country code divided by comma"),
    limit: int = Query(5, description="Number of locations in response", ge=1, le=5)
):
    """
    Get geographical coordinates by city name.
    
    Direct geocoding allows to get geographical coordinates (lat, lon) by using name of the location.
    """
    # Parse city query
    parts = [part.strip() for part in q.split(",")]
    city_name = parts[0]
    state_code = parts[1] if len(parts) > 1 else None
    country_code = parts[2] if len(parts) > 2 else None
    
    if not city_name:
        raise HTTPException(status_code=400, detail="City name cannot be empty")
    
    return await get_coordinates_by_city(
        city_name=city_name,
        state_code=state_code,
        country_code=country_code,
        limit=limit
    )

@router.get("/geocoding/reverse")
async def reverse_geocode(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180),
    limit: int = Query(5, description="Number of locations in response", ge=1, le=5)
):
    """
    Get city name by geographical coordinates.
    
    Reverse geocoding allows to get name of the location (city name or area name) by using geographical coordinates.
    """
    return await get_city_by_coordinates(
        lat=lat,
        lon=lon,
        limit=limit
    )

@router.get("/air-pollution/current")
async def get_air_pollution(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180)
):
    """
    Get current air pollution data.
    
    Current air pollution data for any coordinates on the globe.
    """
    return await get_current_air_pollution(lat=lat, lon=lon)

@router.get("/air-pollution/forecast")
async def get_air_pollution_forecast_data(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180)
):
    """
    Get air pollution forecast.
    
    Air pollution forecast for any coordinates on the globe for 5 days ahead.
    """
    return await get_air_pollution_forecast(lat=lat, lon=lon)

@router.get("/uv-index/current")
async def get_uv_index(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180)
):
    """
    Get current UV index.
    
    Current UV index data for any coordinates on the globe.
    """
    return await get_current_uv_index(lat=lat, lon=lon)

@router.get("/uv-index/forecast")
async def get_uv_forecast(
    lat: float = Query(..., description="Latitude", ge=-90, le=90),
    lon: float = Query(..., description="Longitude", ge=-180, le=180),
    cnt: Optional[int] = Query(None, description="Number of days (max 8)", ge=1, le=8)
):
    """
    Get UV index forecast.
    
    UV index forecast for any coordinates on the globe for up to 8 days ahead.
    """
    return await get_uv_index_forecast(lat=lat, lon=lon, cnt=cnt)

@router.get("/travel-recommendation")
async def get_travel_weather_recommendation(
    lat: Optional[float] = Query(None, description="Latitude", ge=-90, le=90),
    lon: Optional[float] = Query(None, description="Longitude", ge=-180, le=180),
    q: Optional[str] = Query(None, description="City name, state code and country code divided by comma"),
    units: Units = Query(Units.METRIC, description="Units of measurement")
):
    """
    Get travel recommendations based on current weather conditions.
    
    Provides a recommendation score, suggested activities, clothing recommendations,
    and travel tips based on current weather conditions.
    """
    # Validate that at least one location parameter is provided
    if not ((lat is not None and lon is not None) or q):
        raise HTTPException(
            status_code=400, 
            detail="Must provide either coordinates (lat, lon) or city name (q)"
        )
    
    # Get weather data
    if lat is not None and lon is not None:
        weather_data = await get_current_weather_by_coordinates(
            lat=lat, lon=lon, units=units.value
        )
        location_name = weather_data.get("name", f"{lat}, {lon}")
    else:
        # Parse city query
        parts = [part.strip() for part in q.split(",")]
        city_name = parts[0]
        state_code = parts[1] if len(parts) > 1 else None
        country_code = parts[2] if len(parts) > 2 else None
        
        weather_data = await get_current_weather_by_city(
            city_name=city_name,
            state_code=state_code,
            country_code=country_code,
            units=units.value
        )
        location_name = weather_data.get("name", city_name)
    
    # Generate recommendations
    recommendation = get_travel_recommendation(weather_data)
    
    # Create weather summary
    main = weather_data.get("main", {})
    weather = weather_data.get("weather", [{}])[0]
    wind = weather_data.get("wind", {})
    sys = weather_data.get("sys", {})
    
    return {
        "location": location_name,
        "current_weather": {
            "temperature": main.get("temp"),
            "feels_like": main.get("feels_like"),
            "condition": weather.get("main"),
            "description": weather.get("description"),
            "humidity": main.get("humidity"),
            "pressure": main.get("pressure"),
            "wind_speed": wind.get("speed"),
            "wind_direction": wind.get("deg"),
            "cloudiness": weather_data.get("clouds", {}).get("all"),
            "visibility": weather_data.get("visibility"),
            "icon": weather.get("icon"),
            "icon_url": get_weather_icon_url(weather.get("icon", "01d"))
        },
        "recommendation_score": recommendation["score"],
        "recommendation_text": recommendation["recommendation_text"],
        "best_activities": recommendation["activities"],
        "weather_warnings": recommendation["warnings"],
        "clothing_suggestions": recommendation["clothing"],
        "travel_tips": recommendation["tips"],
        "units": units.value
    }

@router.post("/compare")
async def compare_weather_locations(
    locations: List[Dict[str, Any]] = Body(
        ...,
        description="List of locations to compare",
        example=[
            {"name": "London", "city": "London", "country": "UK"},
            {"name": "Paris", "lat": 48.8566, "lon": 2.3522},
            {"name": "New York", "city": "New York", "state": "NY", "country": "US"}
        ]
    ),
    units: Units = Query(Units.METRIC, description="Units of measurement")
):
    """
    Compare weather conditions across multiple locations.
    
    Provide a list of locations with either city information or coordinates.
    Returns weather data for all locations with analysis of extremes.
    """
    if not locations:
        raise HTTPException(status_code=400, detail="At least one location must be provided")
    
    if len(locations) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 locations allowed for comparison")
    
    # Validate locations
    for i, location in enumerate(locations):
        if not location.get("name"):
            raise HTTPException(status_code=400, detail=f"Location {i+1} must have a 'name' field")
        
        has_coords = "lat" in location and "lon" in location
        has_city = "city" in location
        
        if not (has_coords or has_city):
            raise HTTPException(
                status_code=400, 
                detail=f"Location {i+1} must have either coordinates (lat, lon) or city information"
            )
    
    return await get_weather_comparison(locations)

@router.get("/icon/{icon_code}")
async def get_weather_icon(
    icon_code: str,
    size: str = Query("2x", description="Icon size (1x, 2x, 4x)")
):
    """
    Get weather icon URL.
    
    Returns the URL for the weather icon based on the icon code from weather data.
    """
    if size not in ["1x", "2x", "4x"]:
        raise HTTPException(status_code=400, detail="Size must be one of: 1x, 2x, 4x")
    
    icon_url = get_weather_icon_url(icon_code, size)
    
    return {
        "icon_code": icon_code,
        "size": size,
        "url": icon_url
    }

@router.get("/health")
async def health_check():
    """Health check endpoint for OpenWeatherMap integration"""
    return {
        "status": "healthy",
        "service": "openweathermap-weather-api",
        "endpoints": [
            "current/city",
            "current/coordinates", 
            "current/city-id",
            "current/zip",
            "forecast/5day",
            "geocoding/direct",
            "geocoding/reverse",
            "air-pollution/current",
            "air-pollution/forecast",
            "uv-index/current",
            "uv-index/forecast",
            "travel-recommendation",
            "compare"
        ],
        "version": "1.0.0"
    }