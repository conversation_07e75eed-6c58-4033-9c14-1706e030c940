# SynTour项目Bug修复指南

## 🎯 修复目标
确保现有功能全部可以正常运行，修复影响核心功能的严重问题。

## 🔴 严重问题修复方案

### 修复1: WebSocket连接管理 (BUG-001)

**文件**: `backend/main_enhanced.py`
**位置**: 行 1014-1020 和 1072-1077

#### 问题分析
- WebSocket关闭处理不完整
- 异常处理过于宽泛，可能隐藏错误
- 连接清理不彻底

#### 修复方案

**步骤1**: 改进WebSocket关闭处理
```python
# 替换现有的finally块
finally:
    try:
        # 检查WebSocket状态并安全关闭
        if websocket.client_state not in [WebSocketState.DISCONNECTED, WebSocketState.CLOSED]:
            await websocket.close(code=1000, reason="Normal closure")
            logger.info(f"WebSocket closed for client {client_id}")
    except Exception as e:
        logger.error(f"Error closing WebSocket for client {client_id}: {e}")
    finally:
        # 确保连接从管理器中移除
        manager.disconnect(client_id)
```

**步骤2**: 改进ConnectionManager的错误处理
```python
async def send_personal_message(self, message: str, client_id: str):
    if client_id in self.active_connections:
        try:
            websocket = self.active_connections[client_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(message)
            else:
                logger.warning(f"WebSocket not connected for client {client_id}")
                self.disconnect(client_id)
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected during message send")
            self.disconnect(client_id)
        except Exception as e:
            logger.error(f"Failed to send message to client {client_id}: {e}")
            self.disconnect(client_id)
```

---

### 修复2: 音频资源内存泄漏 (BUG-002)

**文件**: `src/ui/VoiceRecorder.tsx`
**位置**: 行 103-120

#### 问题分析
- 音频资源清理不完整
- 缺少null检查
- 可能导致内存泄漏

#### 修复方案

**步骤1**: 改进stopRecording函数
```typescript
const stopRecording = () => {
    try {
        // 安全地清理AudioWorkletNode
        if (processorRef.current) {
            processorRef.current.disconnect();
            processorRef.current = null;
        }

        // 安全地清理MediaStreamAudioSourceNode
        if (sourceRef.current) {
            sourceRef.current.disconnect();
            sourceRef.current = null;
        }

        // 安全地关闭AudioContext
        if (audioContextRef.current) {
            if (audioContextRef.current.state !== 'closed') {
                audioContextRef.current.close();
            }
            audioContextRef.current = null;
        }

        // 停止所有媒体轨道
        if (streamRef.current) {
            streamRef.current.getTracks().forEach((track) => {
                track.stop();
            });
            streamRef.current = null;
        }

        setIsRecording(false);
        console.log("Audio recording stopped and resources cleaned up");
    } catch (error) {
        console.error("Error stopping recording:", error);
        setIsRecording(false);
    }
};
```

**步骤2**: 添加组件卸载时的清理
```typescript
useEffect(() => {
    // 现有的WebSocket初始化代码...
    
    // 返回清理函数
    return () => {
        // 清理WebSocket连接
        if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
        }
        
        // 清理音频资源
        stopRecording();
    };
}, []);
```

---

### 修复3: API错误响应格式统一 (BUG-003)

**文件**: `backend/main_enhanced.py`
**位置**: 多个API端点

#### 问题分析
- 错误响应格式不一致
- 前端错误处理复杂

#### 修复方案

**步骤1**: 定义统一的错误响应模型
```python
from enum import Enum
from datetime import datetime

class ErrorCode(str, Enum):
    VALIDATION_ERROR = "VALIDATION_ERROR"
    PROCESSING_ERROR = "PROCESSING_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    WEBSOCKET_ERROR = "WEBSOCKET_ERROR"
    FILE_ERROR = "FILE_ERROR"

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[ErrorCode] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    details: Optional[Dict[str, Any]] = None
```

**步骤2**: 创建统一的错误处理函数
```python
def create_error_response(
    error_message: str, 
    error_code: ErrorCode = ErrorCode.PROCESSING_ERROR,
    details: Optional[Dict[str, Any]] = None
) -> ErrorResponse:
    return ErrorResponse(
        error=error_message,
        error_code=error_code,
        details=details
    )

def create_websocket_error_message(
    error_message: str,
    error_code: ErrorCode = ErrorCode.WEBSOCKET_ERROR,
    client_id: str = ""
) -> str:
    error_data = {
        "type": "error",
        "error": error_message,
        "error_code": error_code.value,
        "timestamp": datetime.now().isoformat(),
        "client_id": client_id
    }
    return json.dumps(error_data)
```

**步骤3**: 更新所有API端点使用统一格式
```python
# 示例：更新chat_with_ai端点
@app.post("/api/ai/chat", response_model=ChatResponse)
async def chat_with_ai(...):
    try:
        # 现有逻辑...
        pass
    except ValueError as e:
        return create_error_response(
            str(e), 
            ErrorCode.VALIDATION_ERROR
        )
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return create_error_response(
            "Internal server error occurred",
            ErrorCode.PROCESSING_ERROR,
            {"original_error": str(e)}
        )
```

---

## 🟡 中等问题快速修复

### 修复4: WebSocket URL环境变量 (BUG-004)

**文件**: `src/ui/VoiceRecorder.tsx`
```typescript
// 在文件顶部添加
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8002';

// 修改WebSocket连接
useEffect(() => {
    const clientId = `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const ws = new WebSocket(`${WS_BASE_URL}/ws/speech/${clientId}`);
    // 其余代码保持不变...
}, []);
```

### 修复5: 统一文件大小限制 (BUG-005)

**文件**: `backend/main_enhanced.py`
```python
# 在文件顶部定义常量
MAX_FILE_SIZE_MB = 10
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# 更新GCS_UPLOAD_THRESHOLD_MB
GCS_UPLOAD_THRESHOLD_MB = 5  # 小于最大文件大小
```

**文件**: `backend/services/file_processor.py`
```python
# 使用相同的常量
from main_enhanced import MAX_FILE_SIZE_BYTES

class FileProcessor:
    MAX_FILE_SIZE = MAX_FILE_SIZE_BYTES  # 使用统一的限制
```

### 修复6: 添加JSON验证 (BUG-006)

**文件**: `backend/main_enhanced.py`
```python
@app.websocket("/ws/chat/{client_id}")
async def websocket_chat_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            
            # 添加JSON验证
            try:
                message_data = json.loads(data)
                if not isinstance(message_data, dict) or "message" not in message_data:
                    raise ValueError("Invalid message format")
            except (json.JSONDecodeError, ValueError) as e:
                error_msg = create_websocket_error_message(
                    f"Invalid message format: {str(e)}",
                    ErrorCode.VALIDATION_ERROR,
                    client_id
                )
                await manager.send_personal_message(error_msg, client_id)
                continue
            
            # 处理有效消息...
```

---

## 🚀 修复实施计划

### 阶段1: 立即修复 (今天完成)
1. **WebSocket连接管理** - 30分钟
2. **音频资源清理** - 20分钟
3. **环境变量配置** - 10分钟

### 阶段2: 本周完成
1. **统一错误响应格式** - 60分钟
2. **文件大小限制统一** - 15分钟
3. **JSON验证** - 15分钟

### 阶段3: 测试验证
1. **功能测试** - 确保所有功能正常
2. **内存泄漏测试** - 长时间使用语音功能
3. **错误处理测试** - 模拟各种错误情况

---

## ✅ 验证清单

修复完成后，请验证以下功能：

- [ ] 文本聊天功能正常
- [ ] 图片上传和识别功能正常  
- [ ] 语音录制和识别功能正常
- [ ] WebSocket连接稳定，无内存泄漏
- [ ] 错误信息显示一致且友好
- [ ] 多次使用语音功能无问题
- [ ] 文件上传限制正确执行

---

## 📞 支持信息

如果在修复过程中遇到问题，请检查：
1. 环境变量是否正确配置
2. 依赖包是否完整安装
3. Google Cloud服务是否正常
4. 浏览器控制台是否有错误信息
