"use client";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
export default function Register(){
  const [email,setEmail] = useState(""); const [password,setPassword]=useState("");
  const router = useRouter();
  async function submit(e:React.FormEvent){ e.preventDefault();
    const r = await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email,password})});
    if(r.ok) router.push("/auth/login"); else alert("Register failed");
  }
  return (
    <div className="min-h-screen container-page flex items-center">
      <form onSubmit={submit} className="card p-8 w-full max-w-md mx-auto">
        <h1 className="text-2xl font-semibold mb-4">Create your account</h1>
        <input className="input mb-3" type="email" placeholder="Email" value={email} onChange={e=>setEmail(e.target.value)} required/>
        <input className="input mb-4" type="password" placeholder="Password" value={password} onChange={e=>setPassword(e.target.value)} required/>
        <button className="btn btn-primary w-full">Register</button>
        <p className="text-sm mt-3">Already have an account? <Link className="link" href="/auth/login">Sign in</Link></p>
      </form>
    </div>
  );
}
