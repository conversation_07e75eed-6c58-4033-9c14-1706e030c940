class AudioProcessor extends AudioWorkletProcessor {
    process(inputs, outputs, parameters) {
        const input = inputs[0];
        if (input.length > 0) {
            const channelData = input[0];
            const buffer = new ArrayBuffer(channelData.length * 2);
            const view = new DataView(buffer);
            let offset = 0;

            for (let i = 0; i < channelData.length; i++, offset += 2) {
                let s = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
            }

            // Send audio data to main thread
            this.port.postMessage(buffer);
        }
        return true;
    }
}

registerProcessor('audio-processor', AudioProcessor);