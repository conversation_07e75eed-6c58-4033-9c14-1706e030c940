from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Enums for OpenWeatherMap constants
class Units(str, Enum):
    STANDARD = "standard"  # Kelvin, meter/sec, hPa
    METRIC = "metric"      # Celsius, meter/sec, hPa
    IMPERIAL = "imperial"  # Fahrenheit, miles/hour, hPa

class Language(str, Enum):
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    RUSSIAN = "ru"
    CHINESE_SIMPLIFIED = "zh_cn"
    CHINESE_TRADITIONAL = "zh_tw"
    JAPANESE = "ja"
    KOREAN = "ko"
    ARABIC = "ar"

# Base coordinate model
class Coordinates(BaseModel):
    lon: float = Field(..., description="Longitude")
    lat: float = Field(..., description="Latitude")

# Weather condition model
class WeatherCondition(BaseModel):
    id: int = Field(..., description="Weather condition id")
    main: str = Field(..., description="Group of weather parameters (Rain, Snow, Extreme etc.)")
    description: str = Field(..., description="Weather condition within the group")
    icon: str = Field(..., description="Weather icon id")

# Main weather parameters model
class MainWeather(BaseModel):
    temp: float = Field(..., description="Temperature")
    feels_like: float = Field(..., description="Temperature. This temperature parameter accounts for the human perception of weather")
    temp_min: float = Field(..., description="Minimum temperature at the moment")
    temp_max: float = Field(..., description="Maximum temperature at the moment")
    pressure: int = Field(..., description="Atmospheric pressure (on the sea level, if there is no sea_level or grnd_level data), hPa")
    humidity: int = Field(..., description="Humidity, %")
    sea_level: Optional[int] = Field(None, description="Atmospheric pressure on the sea level, hPa")
    grnd_level: Optional[int] = Field(None, description="Atmospheric pressure on the ground level, hPa")

# Wind information model
class Wind(BaseModel):
    speed: float = Field(..., description="Wind speed")
    deg: Optional[int] = Field(None, description="Wind direction, degrees (meteorological)")
    gust: Optional[float] = Field(None, description="Wind gust")

# Clouds information model
class Clouds(BaseModel):
    all: int = Field(..., description="Cloudiness, %")

# Rain information model
class Rain(BaseModel):
    one_hour: Optional[float] = Field(None, alias="1h", description="Rain volume for the last 1 hour, mm")
    three_hours: Optional[float] = Field(None, alias="3h", description="Rain volume for the last 3 hours, mm")

# Snow information model
class Snow(BaseModel):
    one_hour: Optional[float] = Field(None, alias="1h", description="Snow volume for the last 1 hour, mm")
    three_hours: Optional[float] = Field(None, alias="3h", description="Snow volume for the last 3 hours, mm")

# System information model
class SystemInfo(BaseModel):
    type: Optional[int] = Field(None, description="Internal parameter")
    id: Optional[int] = Field(None, description="Internal parameter")
    country: str = Field(..., description="Country code (GB, JP etc.)")
    sunrise: int = Field(..., description="Sunrise time, unix, UTC")
    sunset: int = Field(..., description="Sunset time, unix, UTC")

# Current weather response model
class CurrentWeatherResponse(BaseModel):
    coord: Coordinates = Field(..., description="Coordinates of the location")
    weather: List[WeatherCondition] = Field(..., description="Weather conditions")
    base: str = Field(..., description="Internal parameter")
    main: MainWeather = Field(..., description="Main weather parameters")
    visibility: Optional[int] = Field(None, description="Visibility, meter. The maximum value of the visibility is 10km")
    wind: Optional[Wind] = Field(None, description="Wind information")
    clouds: Optional[Clouds] = Field(None, description="Clouds information")
    rain: Optional[Rain] = Field(None, description="Rain information")
    snow: Optional[Snow] = Field(None, description="Snow information")
    dt: int = Field(..., description="Time of data calculation, unix, UTC")
    sys: SystemInfo = Field(..., description="System information")
    timezone: int = Field(..., description="Shift in seconds from UTC")
    id: int = Field(..., description="City ID")
    name: str = Field(..., description="City name")
    cod: int = Field(..., description="Internal parameter")

# Request models for different search types
class CurrentWeatherByCityRequest(BaseModel):
    """Request model for current weather by city name"""
    q: str = Field(..., description="City name, state code and country code divided by comma")
    appid: str = Field(..., description="API key")
    mode: Optional[str] = Field("json", description="Response format")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

class CurrentWeatherByCoordinatesRequest(BaseModel):
    """Request model for current weather by coordinates"""
    lat: float = Field(..., description="Latitude")
    lon: float = Field(..., description="Longitude")
    appid: str = Field(..., description="API key")
    mode: Optional[str] = Field("json", description="Response format")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

class CurrentWeatherByCityIdRequest(BaseModel):
    """Request model for current weather by city ID"""
    id: int = Field(..., description="City ID")
    appid: str = Field(..., description="API key")
    mode: Optional[str] = Field("json", description="Response format")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

class CurrentWeatherByZipRequest(BaseModel):
    """Request model for current weather by ZIP code"""
    zip: str = Field(..., description="ZIP code and country code divided by comma")
    appid: str = Field(..., description="API key")
    mode: Optional[str] = Field("json", description="Response format")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

# Enhanced weather models with additional information
class WeatherAlert(BaseModel):
    """Weather alert information"""
    sender_name: str = Field(..., description="Name of the alert source")
    event: str = Field(..., description="Alert event name")
    start: int = Field(..., description="Date and time of the start of the alert, Unix, UTC")
    end: int = Field(..., description="Date and time of the end of the alert, Unix, UTC")
    description: str = Field(..., description="Description of the alert")
    tags: Optional[List[str]] = Field(None, description="Type of severe weather")

class AirQuality(BaseModel):
    """Air quality information"""
    aqi: int = Field(..., description="Air Quality Index")
    co: float = Field(..., description="Concentration of CO (Carbon monoxide), μg/m3")
    no: float = Field(..., description="Concentration of NO (Nitric oxide), μg/m3")
    no2: float = Field(..., description="Concentration of NO2 (Nitrogen dioxide), μg/m3")
    o3: float = Field(..., description="Concentration of O3 (Ozone), μg/m3")
    so2: float = Field(..., description="Concentration of SO2 (Sulphur dioxide), μg/m3")
    pm2_5: float = Field(..., description="Concentration of PM2.5 (Fine particles matter), μg/m3")
    pm10: float = Field(..., description="Concentration of PM10 (Coarse particulate matter), μg/m3")
    nh3: float = Field(..., description="Concentration of NH3 (Ammonia), μg/m3")

# UV Index model
class UVIndex(BaseModel):
    """UV Index information"""
    lat: float = Field(..., description="Latitude")
    lon: float = Field(..., description="Longitude")
    date_iso: str = Field(..., description="Date in ISO format")
    date: int = Field(..., description="Date as a Unix timestamp")
    value: float = Field(..., description="UV Index value")

# Geocoding models
class GeocodeLocation(BaseModel):
    """Geocoding location information"""
    name: str = Field(..., description="Name of the found location")
    local_names: Optional[Dict[str, str]] = Field(None, description="Name of the found location in different languages")
    lat: float = Field(..., description="Latitude of the found location")
    lon: float = Field(..., description="Longitude of the found location")
    country: str = Field(..., description="Country of the found location")
    state: Optional[str] = Field(None, description="State of the found location (where available)")

# Error models
class OpenWeatherMapError(BaseModel):
    cod: Union[int, str] = Field(..., description="Error code")
    message: str = Field(..., description="Error message")

class OpenWeatherMapErrorResponse(BaseModel):
    error: OpenWeatherMapError
    timestamp: Optional[str] = None

# Utility models for weather analysis
class WeatherSummary(BaseModel):
    """Summary of weather conditions"""
    location: str
    temperature: float
    feels_like: float
    condition: str
    description: str
    humidity: int
    pressure: int
    wind_speed: float
    wind_direction: Optional[int] = None
    cloudiness: Optional[int] = None
    visibility: Optional[int] = None
    sunrise: datetime
    sunset: datetime
    timezone_offset: int
    units: Units

class WeatherComparison(BaseModel):
    """Comparison between multiple weather locations"""
    locations: List[WeatherSummary]
    warmest_location: Optional[str] = None
    coldest_location: Optional[str] = None
    most_humid_location: Optional[str] = None
    windiest_location: Optional[str] = None

# Historical weather models (for future extension)
class HistoricalWeatherRequest(BaseModel):
    """Request model for historical weather data"""
    lat: float = Field(..., description="Latitude")
    lon: float = Field(..., description="Longitude")
    dt: int = Field(..., description="Date timestamp (Unix time, UTC time zone)")
    appid: str = Field(..., description="API key")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

# Weather forecast models (for future extension)
class ForecastItem(BaseModel):
    """Single forecast item"""
    dt: int = Field(..., description="Time of data forecasted, unix, UTC")
    main: MainWeather = Field(..., description="Main weather parameters")
    weather: List[WeatherCondition] = Field(..., description="Weather conditions")
    clouds: Optional[Clouds] = Field(None, description="Clouds information")
    wind: Optional[Wind] = Field(None, description="Wind information")
    visibility: Optional[int] = Field(None, description="Visibility, meter")
    pop: Optional[float] = Field(None, description="Probability of precipitation")
    rain: Optional[Rain] = Field(None, description="Rain information")
    snow: Optional[Snow] = Field(None, description="Snow information")
    dt_txt: str = Field(..., description="Time of data forecasted, ISO, UTC")

class WeatherForecastResponse(BaseModel):
    """5 day weather forecast response"""
    cod: str = Field(..., description="Internal parameter")
    message: int = Field(..., description="Internal parameter")
    cnt: int = Field(..., description="Number of lines returned by this API call")
    list: List[ForecastItem] = Field(..., description="Forecast data")
    city: Dict[str, Any] = Field(..., description="City information")

# Bulk weather data models
class BulkWeatherRequest(BaseModel):
    """Request model for bulk weather data"""
    bbox: str = Field(..., description="Bounding box [lon-left,lat-bottom,lon-right,lat-top,zoom]")
    appid: str = Field(..., description="API key")
    units: Optional[Units] = Field(Units.STANDARD, description="Units of measurement")
    lang: Optional[Language] = Field(Language.ENGLISH, description="Language")

# Weather map layers (for future extension)
class WeatherMapLayer(str, Enum):
    CLOUDS = "clouds_new"
    PRECIPITATION = "precipitation_new"
    PRESSURE = "pressure_new"
    WIND = "wind_new"
    TEMPERATURE = "temp_new"

class WeatherMapRequest(BaseModel):
    """Request model for weather map tiles"""
    layer: WeatherMapLayer = Field(..., description="Map layer")
    z: int = Field(..., description="Zoom level")
    x: int = Field(..., description="Tile x coordinate")
    y: int = Field(..., description="Tile y coordinate")
    appid: str = Field(..., description="API key")

# Statistics and analytics models
class WeatherStatistics(BaseModel):
    """Weather statistics for analysis"""
    average_temperature: float
    min_temperature: float
    max_temperature: float
    average_humidity: float
    average_pressure: float
    average_wind_speed: float
    most_common_condition: str
    total_locations: int

# Travel weather recommendation models
class TravelWeatherRecommendation(BaseModel):
    """Weather-based travel recommendations"""
    location: str
    current_weather: WeatherSummary
    recommendation_score: float  # 0-10 scale
    recommendation_text: str
    best_activities: List[str]
    weather_warnings: List[str]
    clothing_suggestions: List[str]
    travel_tips: List[str]