link to documentation:
1. https://developers.amadeus.com/self-service/apis-docs/guides/developer-guides/resources/flights/#search-for-best-flight-offers
2. https://developers.amadeus.com/self-service/category/flights/api-doc/flight-offers-search/api-reference

Search for best flight offers
The Flight Offers Search API searches over 500 airlines to find the cheapest flights for a given itinerary. The API lets you search flights between two cities, perform multi-city searches for longer itineraries and access one-way combinable fares to offer the cheapest options possible. For each itinerary, the API provides a list of flight offers with prices, fare details, airline names, baggage allowances and departure terminals.

Tip

Flight Offers Search API is the first step of Flight booking engine flow. Check the details from Video Tutorials and Blog Tutorial.
Warning

Flights from low-cost carriers, American Airlines, Delta and British Airways are unavailable.
The Flight Offers Search API starts the booking cycle with a search for the best fares. The API returns a list of the cheapest flights given a city/airport of departure, a city/airport of arrival, the number and type of passengers and travel dates. The results are complete with airline name and fares as well as additional information, such as bag allowance and pricing for additional baggage.

The API comes in two flavors:

Simple version: GET operation with few parameters but which is quicker to integrate.
On steroids: POST operation offering the full functionalities of the API.
The minimum GET request has following mandatory query parameters:

IATA code for the origin location
IATA code for the destination location
Departure date in the ISO 8601 YYYY-MM-DD format
Number of adult travellers

GET https://test.api.amadus.com/v2/shopping/flight-offers?adults=1&originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-22
Let's have a look at all the optional parameters that we can use to refine the search query. One or more of these parameters can be used in addition to the mandatory query parameters.

Return date in the ISO 8601 YYYY-MM-DD format, same as the departure date:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-22&returnDate=2022-07-26&adults=1
Number of children travelling, same as the number of adults:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&children=1
Number of infants travelling, same as the number of adults:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&infants=1
Travel class, which includes economy, premium economy, business or first:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&travelClass=ECONOMY
We can limit the search to a specific airline by providing its IATA airline code, such as BA for the British Airways:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&includedAirlineCodes=BA
Alternatively, we can exclude an airline from the search in a similar way:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&excludedAirlineCodes=BA
The nonStop parameter filters the search query to direct flights only:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&nonStop=true
The currencyCode defines the currency in which we will see the offer prices:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&currencyCode=EUR
We can limit the maximum price to a certain amount and specify the currency as described above:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&maxPrice=500&currencyCode=EUR
The maximum number of results retrieved can be limited using the max parameter in the search query:


GET https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=BOS&destinationLocationCode=CHI&departureDate=2022-07-26&adults=1&max=1
The API returns a list of flight-offer objects (up to 250), including information such as itineraries, price, pricing options, etc.


"data": [
    {
      "type": "flight-offer",
      "id": "1",
      "source": "GDS",
      "instantTicketingRequired": false,
      "nonHomogeneous": false,
      "oneWay": false,
      "lastTicketingDate": "2022-07-02",
      "numberOfBookableSeats": 9,
      "itineraries": [ ],
      "price": {
        "currency": "EUR",
        "total": "22.00",
        "base": "13.00",
        "fees": [
          {
            "amount": "0.00",
            "type": "SUPPLIER"
          },
          {
            "amount": "0.00",
            "type": "TICKETING"
          }
        ],
        "grandTotal": "22.00"
      }
    }
  ]
The POST endpoint consumes JSON data in the format described below. So, instead of constructing a search query, we can specify all the required parameters in the payload and pass it onto the API in the request body. In addition to this, a X-HTTP-Method-Override header parameter is required.


{
  "currencyCode": "USD",
  "originDestinations": [
    {
      "id": "1",
      "originLocationCode": "RIO",
      "destinationLocationCode": "MAD",
      "departureDateTimeRange": {
        "date": "2022-11-01",
        "time": "10:00:00"
      }
    },
    {
      "id": "2",
      "originLocationCode": "MAD",
      "destinationLocationCode": "RIO",
      "departureDateTimeRange": {
        "date": "2022-11-05",
        "time": "17:00:00"
      }
    }
  ],
  "travelers": [
    {
      "id": "1",
      "travelerType": "ADULT"
    },
    {
      "id": "2",
      "travelerType": "CHILD"
    }
  ],
  "sources": [
    "GDS"
  ],
  "searchCriteria": {
    "maxFlightOffers": 2,
    "flightFilters": {
      "cabinRestrictions": [
        {
          "cabin": "BUSINESS",
          "coverage": "MOST_SEGMENTS",
          "originDestinationIds": [
            "1"
          ]
        }
      ],
      "carrierRestrictions": {
        "excludedCarrierCodes": [
          "AA",
          "TP",
          "AZ"
        ]
      }
    }
  }
}
Search for flights including or excluding specific airlines
If you want your search to return flights with only specified airlines, you can use the parameter includedAirlineCodes to consider specific airlines. For example, there is a traveler who wants to travel from Berlin to Athens only with Aegean Airlines (A3):

GET https://test.api.amadeus.com/v2/shopping/flight-offers?max=3&adults=1&includedAirlineCodes=A3&originLocationCode=BER&destinationLocationCode=ATH&departureDate=2022-12-06

With the parameter excludedAirlineCodes you can ignore specific airlines. For example, there is a traveler who wants to travel from Berlin to Athens ignoring both Aegean Airlines (A3) and Iberia (IB):

GET https://test.api.amadeus.com/v2/shopping/flight-offers?max=3&adults=1&excludedAirlineCodes=A3,IB&originLocationCode=BER&destinationLocationCode=ATH&departureDate=2021-09-06
