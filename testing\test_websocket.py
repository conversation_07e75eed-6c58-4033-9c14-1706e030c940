#!/usr/bin/env python3
"""
测试WebSocket语音识别功能
"""

import asyncio
import websockets
import json
import time

async def test_websocket_connection():
    """测试WebSocket连接和语音识别"""
    
    client_id = f"test_client_{int(time.time())}"
    uri = f"ws://localhost:8002/ws/speech/{client_id}"
    
    print(f"🔗 连接到WebSocket: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功建立")
            
            # 发送测试音频数据（静音）
            print("🎤 发送测试音频数据...")
            
            # 创建1秒的静音音频数据 (16kHz, 16-bit, mono)
            sample_rate = 16000
            duration = 2  # 2秒
            audio_data = b'\x00' * (sample_rate * 2 * duration)
            
            # 分块发送音频数据
            chunk_size = 1024
            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i + chunk_size]
                await websocket.send(chunk)
                await asyncio.sleep(0.1)  # 模拟实时音频流
            
            print("📡 音频数据发送完成，等待响应...")
            
            # 等待响应
            timeout = 10  # 10秒超时
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 等待消息，超时时间为1秒
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    
                    try:
                        data = json.loads(message)
                        print(f"📨 收到消息: {data}")
                        
                        if data.get("type") == "transcript":
                            transcript = data.get("transcript", "")
                            is_final = data.get("is_final", False)
                            confidence = data.get("confidence", 0.0)
                            print(f"🎯 转录结果: '{transcript}' (最终: {is_final}, 置信度: {confidence})")
                            
                        elif data.get("type") == "response":
                            response = data.get("response", "")
                            print(f"🤖 AI响应: {response}")
                            
                        elif data.get("type") == "error":
                            error = data.get("error", "")
                            print(f"❌ 错误: {error}")
                            
                    except json.JSONDecodeError:
                        print(f"📨 收到非JSON消息: {message}")
                        
                except asyncio.TimeoutError:
                    # 超时是正常的，继续等待
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket连接已关闭")
                    break
            
            print("⏰ 测试完成")
            return True
            
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的WebSocket URI")
        return False
    except websockets.exceptions.ConnectionRefused:
        print("❌ WebSocket连接被拒绝，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

async def test_health_check():
    """测试健康检查端点"""
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8002/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 WebSocket语音识别测试")
    print("=" * 50)
    
    results = []
    
    # 测试健康检查
    print("🔍 测试健康检查...")
    results.append(("健康检查", await test_health_check()))
    
    print()
    
    # 测试WebSocket连接
    print("🔍 测试WebSocket连接...")
    results.append(("WebSocket连接", await test_websocket_connection()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！WebSocket语音识别功能正常。")
        print("\n📋 功能状态:")
        print("  ✅ API密钥配置正确")
        print("  ✅ WebSocket连接稳定")
        print("  ✅ 语音识别服务可用")
        print("  ✅ 错误处理正常")
    else:
        print("⚠️ 部分测试失败，请检查:")
        print("  1. 确保后端服务正在运行")
        print("  2. 检查API密钥配置")
        print("  3. 验证网络连接")
    
    return all_passed

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
