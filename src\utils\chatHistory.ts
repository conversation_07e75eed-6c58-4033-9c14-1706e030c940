import { Message } from "@/ui/ChatInterface";

export interface ChatSession {
  id: string;
  title: string;
  createdAt: Date;
  lastMessage: string;
  messageCount: number;
  messages: Message[];
}

const STORAGE_KEY = 'syntour.chats';

// 生成聊天会话标题
export function generateChatTitle(messages: Message[]): string {
  if (messages.length === 0) return "新对话";
  
  const firstUserMessage = messages.find(msg => msg.type === 'user');
  if (firstUserMessage) {
    const content = firstUserMessage.content.trim();
    // 取前30个字符作为标题
    if (content.length > 30) {
      return content.substring(0, 30) + "...";
    }
    return content;
  }
  
  return "新对话";
}

// 保存聊天会话
export function saveChatSession(messages: Message[]): string {
  if (messages.length === 0) return "";
  
  try {
    const sessions = loadChatSessions();
    const sessionId = Date.now().toString();
    const title = generateChatTitle(messages);
    const lastMessage = messages[messages.length - 1]?.content || "";
    
    const newSession: ChatSession = {
      id: sessionId,
      title,
      createdAt: new Date(),
      lastMessage: lastMessage.length > 100 ? lastMessage.substring(0, 100) + "..." : lastMessage,
      messageCount: messages.length,
      messages: messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    };
    
    // 添加到会话列表的开头
    sessions.unshift(newSession);
    
    // 只保留最近的50个会话
    const limitedSessions = sessions.slice(0, 50);
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(limitedSessions));
    return sessionId;
  } catch (error) {
    console.error('Failed to save chat session:', error);
    return "";
  }
}

// 加载所有聊天会话
export function loadChatSessions(): ChatSession[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    
    const parsed = JSON.parse(stored);
    return parsed.map((session: any) => ({
      ...session,
      createdAt: new Date(session.createdAt),
      messages: session.messages?.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      })) || []
    }));
  } catch (error) {
    console.error('Failed to load chat sessions:', error);
    return [];
  }
}

// 加载特定会话的消息
export function loadChatSession(sessionId: string): Message[] {
  try {
    const sessions = loadChatSessions();
    const session = sessions.find(s => s.id === sessionId);
    return session?.messages || [];
  } catch (error) {
    console.error('Failed to load chat session:', error);
    return [];
  }
}

// 删除聊天会话
export function deleteChatSession(sessionId: string): void {
  try {
    const sessions = loadChatSessions();
    const filteredSessions = sessions.filter(s => s.id !== sessionId);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredSessions));
  } catch (error) {
    console.error('Failed to delete chat session:', error);
  }
}

// 清空所有聊天记录
export function clearAllChatSessions(): void {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Failed to clear chat sessions:', error);
  }
}

// 更新聊天会话
export function updateChatSession(sessionId: string, messages: Message[]): void {
  if (messages.length === 0) return;
  
  try {
    const sessions = loadChatSessions();
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);
    
    if (sessionIndex !== -1) {
      const title = generateChatTitle(messages);
      const lastMessage = messages[messages.length - 1]?.content || "";
      
      sessions[sessionIndex] = {
        ...sessions[sessionIndex],
        title,
        lastMessage: lastMessage.length > 100 ? lastMessage.substring(0, 100) + "..." : lastMessage,
        messageCount: messages.length,
        messages: messages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      };
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(sessions));
    }
  } catch (error) {
    console.error('Failed to update chat session:', error);
  }
}
