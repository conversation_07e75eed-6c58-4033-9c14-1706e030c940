import os
from google.cloud import speech

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = r"C:\Users\<USER>\Desktop\TRAVEL_AI\silver-course-470107-h0-de21f66e46dc.json"

client = speech.SpeechClient()

with open("testoneaudio.mp3", "rb") as f:
    content = f.read()

audio = speech.RecognitionAudio(content=content)
config = speech.RecognitionConfig(
    encoding=speech.RecognitionConfig.AudioEncoding.MP3,
    sample_rate_hertz=16000,
    language_code="en-US"
)


response = client.recognize(config=config, audio=audio)

for result in response.results:
    print("Transcript:", result.alternatives[0].transcript)
