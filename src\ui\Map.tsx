import React from "react";
import { GoogleMap, use<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@react-google-maps/api";

const containerStyle = { width: "100%", height: "100vh" };
const center = { lat: 5.4164, lng: 100.3327 }; // Penang

const Map: React.FC = () => {
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: process.env.GOOGLE_MAPS_JAVASCRIPT_API_KEY || "",
  });

  return isLoaded ? (
    <GoogleMap mapContainerStyle={containerStyle} center={center} zoom={12}>
      <Marker position={center} />
    </GoogleMap>
  ) : (
    <p>Loading map...</p>
  );
};

export default Map;
