#!/usr/bin/env python3
"""
SynTour项目Bug修复验证脚本
用于测试修复后的功能是否正常工作
"""

import asyncio
import websockets
import json
import time
import requests
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
API_BASE_URL = "http://localhost:8002"
WS_BASE_URL = "ws://localhost:8002"

async def test_websocket_connection():
    """测试WebSocket连接管理"""
    logger.info("🔍 测试WebSocket连接管理...")
    
    client_id = f"test_client_{int(time.time())}"
    uri = f"{WS_BASE_URL}/ws/speech/{client_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket连接成功")
            
            # 发送测试数据
            test_data = b"test audio data"
            await websocket.send(test_data)
            logger.info("📤 发送测试音频数据")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📥 收到响应: {response}")
                
                # 解析响应
                try:
                    parsed = json.loads(response)
                    if "error_code" in parsed:
                        logger.info("✅ 错误响应格式正确")
                    else:
                        logger.info("📋 收到正常响应")
                except json.JSONDecodeError:
                    logger.warning("⚠️ 响应不是JSON格式")
                    
            except asyncio.TimeoutError:
                logger.warning("⏰ 5秒内未收到响应")
                
        logger.info("✅ WebSocket连接正常关闭")
        
    except Exception as e:
        logger.error(f"❌ WebSocket测试失败: {e}")
        return False
    
    return True

async def test_websocket_chat():
    """测试WebSocket聊天功能"""
    logger.info("🔍 测试WebSocket聊天功能...")
    
    client_id = f"test_chat_{int(time.time())}"
    uri = f"{WS_BASE_URL}/ws/chat/{client_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("✅ 聊天WebSocket连接成功")
            
            # 测试有效消息
            valid_message = json.dumps({"message": "Hello, this is a test message"})
            await websocket.send(valid_message)
            logger.info("📤 发送有效聊天消息")
            
            # 测试无效消息格式
            invalid_message = "invalid json"
            await websocket.send(invalid_message)
            logger.info("📤 发送无效消息格式")
            
            # 等待响应
            for i in range(2):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    parsed = json.loads(response)
                    
                    if parsed.get("type") == "error" and "error_code" in parsed:
                        logger.info("✅ 错误处理和格式正确")
                    else:
                        logger.info(f"📋 收到响应: {parsed.get('type', 'unknown')}")
                        
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ 第{i+1}个响应超时")
                except json.JSONDecodeError:
                    logger.warning("⚠️ 响应不是JSON格式")
                    
        logger.info("✅ 聊天WebSocket连接正常关闭")
        
    except Exception as e:
        logger.error(f"❌ 聊天WebSocket测试失败: {e}")
        return False
    
    return True

def test_api_endpoints():
    """测试API端点"""
    logger.info("🔍 测试API端点...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ 健康检查端点正常")
        else:
            logger.warning(f"⚠️ 健康检查返回状态码: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return False
    
    # 测试根端点
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ 根端点正常: {data.get('message', 'No message')}")
        else:
            logger.warning(f"⚠️ 根端点返回状态码: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ 根端点测试失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    logger.info("🚀 开始SynTour项目Bug修复验证测试")
    logger.info("=" * 50)
    
    results = []
    
    # 测试API端点
    logger.info("\n📡 测试API端点...")
    api_result = test_api_endpoints()
    results.append(("API端点", api_result))
    
    # 测试WebSocket连接
    logger.info("\n🔌 测试WebSocket连接...")
    ws_result = await test_websocket_connection()
    results.append(("WebSocket连接", ws_result))
    
    # 测试WebSocket聊天
    logger.info("\n💬 测试WebSocket聊天...")
    chat_result = await test_websocket_chat()
    results.append(("WebSocket聊天", chat_result))
    
    # 输出测试结果
    logger.info("\n" + "=" * 50)
    logger.info("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！Bug修复成功。")
    else:
        logger.info("\n⚠️ 部分测试失败，请检查相关功能。")
    
    return all_passed

if __name__ == "__main__":
    print("SynTour项目Bug修复验证测试")
    print("请确保后端服务器正在运行 (python backend/main_enhanced.py)")
    print("按Enter键开始测试...")
    input()
    
    result = asyncio.run(main())
    exit(0 if result else 1)
