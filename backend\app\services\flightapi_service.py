# app/services/flightapi_service.py
import os
import httpx
import logging
from typing import Optional, Dict, Any, List
from fastapi import HTTPException
import json
from datetime import datetime, date, timedelta

logger = logging.getLogger(__name__)

# FlightAPI Configuration
def get_flightapi_credentials():
    """Get FlightAPI credentials and endpoints from environment variables"""
    api_key = os.getenv('FLIGHT_API_KEY')
    oneway_endpoint = os.getenv('ONEWAY_TRIP_API_ENDPOINT', 'https://api.flightapi.io/onewaytrip')
    roundtrip_endpoint = os.getenv('ROUND_TRIP_API_ENDPOINT', 'https://api.flightapi.io/roundtrip')
    schedule_endpoint = os.getenv('AIRPORT_SCHEDULE_API_ENDPOINT', 'https://api.flightapi.io/schedule')
    
    if not api_key:
        raise ValueError("FLIGHT_API_KEY environment variable must be set")
    
    return {
        'api_key': api_key,
        'oneway_endpoint': oneway_endpoint,
        'roundtrip_endpoint': roundtrip_endpoint,
        'schedule_endpoint': schedule_endpoint
    }

async def flightapi_request(
    endpoint: str, 
    params: Optional[Dict[str, Any]] = None, 
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None
) -> dict:
    """Generic FlightAPI request with error handling"""
    try:
        config = get_flightapi_credentials()
        
        # Prepare headers
        headers = {
            "Accept": "application/json",
            "User-Agent": "SynTour-Travel-App/1.0"
        }
        
        # Add API key to parameters
        request_params = params or {}
        request_params['apikey'] = config['api_key']
        
        # Add content-type for POST requests
        if method.upper() == "POST" and data:
            headers["Content-Type"] = "application/json"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info(f"Making {method} request to: {endpoint}")
            
            if method.upper() == "GET":
                response = await client.get(endpoint, headers=headers, params=request_params)
            elif method.upper() == "POST":
                response = await client.post(endpoint, headers=headers, params=request_params, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # Handle different response status codes
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    # If response is not JSON, return text content
                    return {"success": False, "message": "Invalid JSON response", "content": response.text}
            elif response.status_code == 204:
                return {"success": True, "message": "No content found", "data": []}
            elif response.status_code == 400:
                error_detail = response.text
                try:
                    error_json = response.json()
                    error_detail = error_json.get("message", error_detail)
                except:
                    pass
                logger.error(f"Bad request to FlightAPI: {error_detail}")
                raise HTTPException(status_code=400, detail=f"Invalid request: {error_detail}")
            elif response.status_code == 401:
                logger.error("FlightAPI authentication failed")
                raise HTTPException(status_code=401, detail="Invalid API key")
            elif response.status_code == 403:
                logger.error("FlightAPI access forbidden")
                raise HTTPException(status_code=403, detail="Access forbidden - check API key permissions")
            elif response.status_code == 429:
                logger.error("FlightAPI rate limit exceeded")
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            elif response.status_code == 500:
                logger.error("FlightAPI internal server error")
                raise HTTPException(status_code=500, detail="FlightAPI service error")
            else:
                logger.error(f"FlightAPI error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"FlightAPI error: {response.text}"
                )
                
    except httpx.TimeoutException:
        logger.error("Request timeout to FlightAPI")
        raise HTTPException(status_code=408, detail="Request timeout")
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise HTTPException(status_code=503, detail="FlightAPI service unavailable")
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# One-way trip search functions
async def search_oneway_flights(
    origin: str,
    destination: str,
    departure_date: str,
    adults: int = 1,
    children: Optional[int] = None,
    infants: Optional[int] = None,
    cabin_class: Optional[str] = None,
    currency: str = "USD",
    max_results: int = 50,
    sort_by: str = "price",
    max_stops: Optional[int] = None,
    airlines: Optional[List[str]] = None,
    exclude_airlines: Optional[List[str]] = None,
    max_price: Optional[float] = None,
    departure_time_from: Optional[str] = None,
    departure_time_to: Optional[str] = None
) -> dict:
    """Search for one-way flights"""
    config = get_flightapi_credentials()
    
    params = {
        "origin": origin.upper(),
        "destination": destination.upper(),
        "departure_date": departure_date,
        "adults": adults,
        "currency": currency,
        "max_results": max_results,
        "sort_by": sort_by
    }
    
    # Add optional parameters
    if children:
        params["children"] = children
    if infants:
        params["infants"] = infants
    if cabin_class:
        params["cabin_class"] = cabin_class
    if max_stops is not None:
        params["max_stops"] = max_stops
    if airlines:
        params["airlines"] = ",".join(airlines)
    if exclude_airlines:
        params["exclude_airlines"] = ",".join(exclude_airlines)
    if max_price:
        params["max_price"] = max_price
    if departure_time_from:
        params["departure_time_from"] = departure_time_from
    if departure_time_to:
        params["departure_time_to"] = departure_time_to
    
    return await flightapi_request(config['oneway_endpoint'], params=params)

# Round-trip search functions
async def search_roundtrip_flights(
    origin: str,
    destination: str,
    departure_date: str,
    return_date: str,
    adults: int = 1,
    children: Optional[int] = None,
    infants: Optional[int] = None,
    cabin_class: Optional[str] = None,
    currency: str = "USD",
    max_results: int = 50,
    sort_by: str = "price",
    max_stops: Optional[int] = None,
    airlines: Optional[List[str]] = None,
    exclude_airlines: Optional[List[str]] = None,
    max_price: Optional[float] = None,
    departure_time_from: Optional[str] = None,
    departure_time_to: Optional[str] = None,
    return_time_from: Optional[str] = None,
    return_time_to: Optional[str] = None
) -> dict:
    """Search for round-trip flights"""
    config = get_flightapi_credentials()
    
    params = {
        "origin": origin.upper(),
        "destination": destination.upper(),
        "departure_date": departure_date,
        "return_date": return_date,
        "adults": adults,
        "currency": currency,
        "max_results": max_results,
        "sort_by": sort_by
    }
    
    # Add optional parameters
    if children:
        params["children"] = children
    if infants:
        params["infants"] = infants
    if cabin_class:
        params["cabin_class"] = cabin_class
    if max_stops is not None:
        params["max_stops"] = max_stops
    if airlines:
        params["airlines"] = ",".join(airlines)
    if exclude_airlines:
        params["exclude_airlines"] = ",".join(exclude_airlines)
    if max_price:
        params["max_price"] = max_price
    if departure_time_from:
        params["departure_time_from"] = departure_time_from
    if departure_time_to:
        params["departure_time_to"] = departure_time_to
    if return_time_from:
        params["return_time_from"] = return_time_from
    if return_time_to:
        params["return_time_to"] = return_time_to
    
    return await flightapi_request(config['roundtrip_endpoint'], params=params)

# Airport schedule functions
async def get_airport_schedule(
    airport: str,
    date: str,
    schedule_type: str = "both",
    limit: int = 100,
    airline: Optional[str] = None,
    flight_number: Optional[str] = None
) -> dict:
    """Get airport schedule for departures and/or arrivals"""
    config = get_flightapi_credentials()
    
    params = {
        "airport": airport.upper(),
        "date": date,
        "type": schedule_type,
        "limit": limit
    }
    
    # Add optional parameters
    if airline:
        params["airline"] = airline.upper()
    if flight_number:
        params["flight_number"] = flight_number
    
    return await flightapi_request(config['schedule_endpoint'], params=params)

async def get_airport_departures(
    airport: str,
    date: str,
    limit: int = 100,
    airline: Optional[str] = None
) -> dict:
    """Get airport departures for a specific date"""
    return await get_airport_schedule(
        airport=airport,
        date=date,
        schedule_type="departures",
        limit=limit,
        airline=airline
    )

async def get_airport_arrivals(
    airport: str,
    date: str,
    limit: int = 100,
    airline: Optional[str] = None
) -> dict:
    """Get airport arrivals for a specific date"""
    return await get_airport_schedule(
        airport=airport,
        date=date,
        schedule_type="arrivals",
        limit=limit,
        airline=airline
    )

# Utility functions
def validate_date_format(date_string: str) -> bool:
    """Validate date string is in YYYY-MM-DD format"""
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False

def validate_time_format(time_string: str) -> bool:
    """Validate time string is in HH:MM format"""
    try:
        datetime.strptime(time_string, "%H:%M")
        return True
    except ValueError:
        return False

def validate_iata_code(code: str) -> bool:
    """Validate IATA code format (3 letters)"""
    return len(code) == 3 and code.isalpha() and code.isupper()

def validate_airline_code(code: str) -> bool:
    """Validate airline code format (2-3 letters)"""
    return 2 <= len(code) <= 3 and code.isalpha() and code.isupper()

def format_duration(duration_minutes: int) -> str:
    """Format duration from minutes to human readable format"""
    if duration_minutes < 60:
        return f"{duration_minutes}m"
    
    hours = duration_minutes // 60
    minutes = duration_minutes % 60
    
    if minutes == 0:
        return f"{hours}h"
    else:
        return f"{hours}h {minutes}m"

def parse_duration(duration_str: str) -> int:
    """Parse duration string to minutes"""
    try:
        # Handle formats like "2h 30m", "2h", "30m"
        duration_str = duration_str.lower().strip()
        total_minutes = 0
        
        if 'h' in duration_str:
            hours_part = duration_str.split('h')[0].strip()
            total_minutes += int(hours_part) * 60
            duration_str = duration_str.split('h')[1].strip()
        
        if 'm' in duration_str and duration_str != '':
            minutes_part = duration_str.split('m')[0].strip()
            if minutes_part:
                total_minutes += int(minutes_part)
        
        return total_minutes
    except:
        return 0

def calculate_flight_statistics(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate statistics for flight search results"""
    if not flights:
        return {
            "total_results": 0,
            "average_price": None,
            "min_price": None,
            "max_price": None,
            "direct_flights": 0,
            "connecting_flights": 0
        }
    
    prices = []
    direct_count = 0
    connecting_count = 0
    
    for flight in flights:
        # Extract price information
        if "price" in flight and "total" in flight["price"]:
            try:
                price = float(flight["price"]["total"]["total"])
                prices.append(price)
            except (ValueError, KeyError):
                pass
        
        # Count direct vs connecting flights
        if "itineraries" in flight:
            for itinerary in flight["itineraries"]:
                if "segments" in itinerary:
                    if len(itinerary["segments"]) == 1:
                        direct_count += 1
                    else:
                        connecting_count += 1
    
    statistics = {
        "total_results": len(flights),
        "direct_flights": direct_count,
        "connecting_flights": connecting_count
    }
    
    if prices:
        statistics.update({
            "average_price": round(sum(prices) / len(prices), 2),
            "min_price": min(prices),
            "max_price": max(prices)
        })
    else:
        statistics.update({
            "average_price": None,
            "min_price": None,
            "max_price": None
        })
    
    return statistics

def filter_flights_by_criteria(
    flights: List[Dict[str, Any]],
    max_price: Optional[float] = None,
    max_stops: Optional[int] = None,
    airlines: Optional[List[str]] = None,
    exclude_airlines: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Filter flights based on additional criteria"""
    filtered_flights = []
    
    for flight in flights:
        # Price filter
        if max_price:
            try:
                flight_price = float(flight["price"]["total"]["total"])
                if flight_price > max_price:
                    continue
            except (KeyError, ValueError):
                continue
        
        # Stops filter
        if max_stops is not None:
            max_flight_stops = 0
            if "itineraries" in flight:
                for itinerary in flight["itineraries"]:
                    if "segments" in itinerary:
                        stops = len(itinerary["segments"]) - 1
                        max_flight_stops = max(max_flight_stops, stops)
            
            if max_flight_stops > max_stops:
                continue
        
        # Airline filters
        flight_airlines = set()
        if "itineraries" in flight:
            for itinerary in flight["itineraries"]:
                if "segments" in itinerary:
                    for segment in itinerary["segments"]:
                        if "airline" in segment and "iata" in segment["airline"]:
                            flight_airlines.add(segment["airline"]["iata"])
        
        # Include specific airlines
        if airlines:
            if not any(airline in flight_airlines for airline in airlines):
                continue
        
        # Exclude specific airlines
        if exclude_airlines:
            if any(airline in flight_airlines for airline in exclude_airlines):
                continue
        
        filtered_flights.append(flight)
    
    return filtered_flights

def sort_flights(flights: List[Dict[str, Any]], sort_by: str = "price") -> List[Dict[str, Any]]:
    """Sort flights based on specified criteria"""
    if not flights:
        return flights
    
    try:
        if sort_by == "price":
            return sorted(flights, key=lambda x: float(x.get("price", {}).get("total", {}).get("total", float('inf'))))
        elif sort_by == "duration":
            def get_duration(flight):
                total_duration = 0
                if "itineraries" in flight:
                    for itinerary in flight["itineraries"]:
                        if "duration" in itinerary:
                            total_duration += parse_duration(itinerary["duration"])
                return total_duration
            return sorted(flights, key=get_duration)
        elif sort_by == "departure_time":
            def get_departure_time(flight):
                if "itineraries" in flight and flight["itineraries"]:
                    first_itinerary = flight["itineraries"][0]
                    if "segments" in first_itinerary and first_itinerary["segments"]:
                        first_segment = first_itinerary["segments"][0]
                        if "departure" in first_segment and "time" in first_segment["departure"]:
                            return first_segment["departure"]["time"].get("scheduled", "")
                return ""
            return sorted(flights, key=get_departure_time)
        else:
            return flights
    except Exception as e:
        logger.warning(f"Error sorting flights: {e}")
        return flights