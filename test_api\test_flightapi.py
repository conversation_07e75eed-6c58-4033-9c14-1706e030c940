#!/usr/bin/env python3
"""
FlightAPI Test Script

This script tests the FlightAPI integration using the updated
service layer and models. It demonstrates various API endpoints and validates
the response structure against our Pydantic models.

Documentation: https://docs.flightapi.io/

## Oneway Trip API
- https://docs.flightapi.io/flight-price-api/oneway-trip-api

## Round Trip API
- https://docs.flightapi.io/flight-price-api/round-trip-api

## Airport Schedule API
- https://docs.flightapi.io/airport-schedule-api
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime, date, timedelta

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.flightapi_service import (
    search_oneway_flights,
    search_roundtrip_flights,
    get_airport_schedule,
    get_airport_departures,
    get_airport_arrivals,
    calculate_flight_statistics,
    validate_date_format,
    validate_iata_code
)
from app.models.flightapi_model import (
    OnewayTripResponse,
    RoundTripResponse,
    AirportScheduleResponse,
    CabinClass,
    SortBy
)

class FlightAPITester:
    """Test class for FlightAPI endpoints"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_oneway_flights(self):
        """Test one-way flight search endpoint"""
        try:
            print("\n✈️ Testing One-way Flight Search...")
            
            # Test basic one-way search (BOS to CHI)
            tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
            
            response = await search_oneway_flights(
                origin="BOS",
                destination="CHI",
                departure_date=tomorrow,
                adults=1,
                max_results=5
            )
            
            # Validate response structure
            if response.get("success", False):
                flights_count = len(response.get("data", []))
                self.log_test(
                    "One-way Flights - Basic Search",
                    True,
                    {"flights_count": flights_count, "route": "BOS-CHI"}
                )
                
                # Test first flight structure
                if flights_count > 0:
                    first_flight = response["data"][0]
                    required_fields = ["price", "itineraries"]
                    missing_fields = [field for field in required_fields if field not in first_flight]
                    
                    self.log_test(
                        "One-way Flight Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields,
                            "available_fields": list(first_flight.keys())[:10],
                            "has_price": "price" in first_flight
                        }
                    )
                else:
                    self.log_test("One-way Flight Structure", False, {"error": "No flights returned"})
            else:
                error_msg = response.get("message", "Unknown error")
                self.log_test("One-way Flights - Basic Search", False, {"error": error_msg})
            
            # Test with filters
            filtered_response = await search_oneway_flights(
                origin="NYC",
                destination="LAX",
                departure_date=tomorrow,
                adults=1,
                max_results=3,
                cabin_class="Economy",
                max_stops=1
            )
            
            self.log_test(
                "One-way Flights - With Filters",
                filtered_response.get("success", False),
                {"route": "NYC-LAX", "filters": "Economy, max_stops=1"}
            )
            
        except Exception as e:
            self.log_test("One-way Flights API", False, {"error": str(e)})
    
    async def test_roundtrip_flights(self):
        """Test round-trip flight search endpoint"""
        try:
            print("\n🔄 Testing Round-trip Flight Search...")
            
            # Test basic round-trip search
            tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
            next_week = (date.today() + timedelta(days=7)).strftime("%Y-%m-%d")
            
            response = await search_roundtrip_flights(
                origin="BOS",
                destination="CHI",
                departure_date=tomorrow,
                return_date=next_week,
                adults=1,
                max_results=5
            )
            
            # Validate response structure
            if response.get("success", False):
                flights_count = len(response.get("data", []))
                self.log_test(
                    "Round-trip Flights - Basic Search",
                    True,
                    {"flights_count": flights_count, "route": "BOS-CHI"}
                )
                
                # Test round-trip structure
                if flights_count > 0:
                    first_flight = response["data"][0]
                    has_outbound_return = False
                    
                    if "itineraries" in first_flight:
                        # Round-trip should have 2 itineraries (outbound + return)
                        has_outbound_return = len(first_flight["itineraries"]) >= 1
                    
                    self.log_test(
                        "Round-trip Flight Structure",
                        has_outbound_return,
                        {
                            "itineraries_count": len(first_flight.get("itineraries", [])),
                            "has_price": "price" in first_flight
                        }
                    )
                else:
                    self.log_test("Round-trip Flight Structure", False, {"error": "No flights returned"})
            else:
                error_msg = response.get("message", "Unknown error")
                self.log_test("Round-trip Flights - Basic Search", False, {"error": error_msg})
            
            # Test with multiple passengers
            family_response = await search_roundtrip_flights(
                origin="NYC",
                destination="MIA",
                departure_date=tomorrow,
                return_date=next_week,
                adults=2,
                children=1,
                max_results=3
            )
            
            self.log_test(
                "Round-trip Flights - Multiple Passengers",
                family_response.get("success", False),
                {"route": "NYC-MIA", "passengers": "2 adults, 1 child"}
            )
            
        except Exception as e:
            self.log_test("Round-trip Flights API", False, {"error": str(e)})
    
    async def test_airport_schedule(self):
        """Test airport schedule endpoint"""
        try:
            print("\n🏢 Testing Airport Schedule...")
            
            # Test airport schedule for today
            today = date.today().strftime("%Y-%m-%d")
            
            response = await get_airport_schedule(
                airport="BOS",
                date=today,
                schedule_type="both",
                limit=10
            )
            
            # Validate response structure
            if response.get("success", False):
                schedule_data = response.get("data", {})
                self.log_test(
                    "Airport Schedule - Basic Request",
                    True,
                    {
                        "airport": "BOS",
                        "date": today,
                        "has_departures": "departures" in schedule_data,
                        "has_arrivals": "arrivals" in schedule_data
                    }
                )
                
                # Test schedule data structure
                if schedule_data:
                    required_fields = ["airport", "date"]
                    missing_fields = [field for field in required_fields if field not in schedule_data]
                    
                    self.log_test(
                        "Airport Schedule Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields,
                            "available_fields": list(schedule_data.keys())
                        }
                    )
                else:
                    self.log_test("Airport Schedule Structure", False, {"error": "No schedule data returned"})
            else:
                error_msg = response.get("message", "Unknown error")
                self.log_test("Airport Schedule - Basic Request", False, {"error": error_msg})
            
            # Test departures only
            departures_response = await get_airport_departures(
                airport="LAX",
                date=today,
                limit=5
            )
            
            self.log_test(
                "Airport Schedule - Departures Only",
                departures_response.get("success", False),
                {"airport": "LAX", "type": "departures"}
            )
            
            # Test arrivals only
            arrivals_response = await get_airport_arrivals(
                airport="JFK",
                date=today,
                limit=5
            )
            
            self.log_test(
                "Airport Schedule - Arrivals Only",
                arrivals_response.get("success", False),
                {"airport": "JFK", "type": "arrivals"}
            )
            
        except Exception as e:
            self.log_test("Airport Schedule API", False, {"error": str(e)})
    
    async def test_validation_functions(self):
        """Test validation utility functions"""
        try:
            print("\n🔍 Testing Validation Functions...")
            
            # Test IATA code validation
            valid_codes = ["BOS", "CHI", "NYC", "LAX"]
            invalid_codes = ["BOST", "CH", "123", "bo"]
            
            valid_results = [validate_iata_code(code) for code in valid_codes]
            invalid_results = [validate_iata_code(code) for code in invalid_codes]
            
            self.log_test(
                "IATA Code Validation",
                all(valid_results) and not any(invalid_results),
                {
                    "valid_codes": valid_codes,
                    "invalid_codes": invalid_codes,
                    "valid_results": valid_results,
                    "invalid_results": invalid_results
                }
            )
            
            # Test date format validation
            valid_dates = ["2024-12-25", "2025-01-01", "2025-06-15"]
            invalid_dates = ["2024/12/25", "25-12-2024", "2024-13-01", "invalid"]
            
            valid_date_results = [validate_date_format(date_str) for date_str in valid_dates]
            invalid_date_results = [validate_date_format(date_str) for date_str in invalid_dates]
            
            self.log_test(
                "Date Format Validation",
                all(valid_date_results) and not any(invalid_date_results),
                {
                    "valid_dates": valid_dates,
                    "invalid_dates": invalid_dates
                }
            )
            
        except Exception as e:
            self.log_test("Validation Functions", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid airport code
            try:
                await search_oneway_flights(
                    origin="INVALID",
                    destination="CHI",
                    departure_date=(date.today() + timedelta(days=1)).strftime("%Y-%m-%d"),
                    adults=1
                )
                self.log_test("Error Handling - Invalid Airport", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Airport", True, {"expected_error": str(e)})
            
            # Test past date
            try:
                yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
                await search_oneway_flights(
                    origin="BOS",
                    destination="CHI",
                    departure_date=yesterday,
                    adults=1
                )
                self.log_test("Error Handling - Past Date", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Past Date", True, {"expected_error": str(e)})
            
            # Test invalid return date (before departure)
            try:
                tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
                today = date.today().strftime("%Y-%m-%d")
                await search_roundtrip_flights(
                    origin="BOS",
                    destination="CHI",
                    departure_date=tomorrow,
                    return_date=today,
                    adults=1
                )
                self.log_test("Error Handling - Invalid Return Date", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Return Date", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def test_statistics_calculation(self):
        """Test flight statistics calculation"""
        try:
            print("\n📊 Testing Statistics Calculation...")
            
            # Mock flight data for testing
            mock_flights = [
                {
                    "price": {"total": {"total": "200.00"}},
                    "itineraries": [{"segments": [{"airline": {"iata": "AA"}}]}]
                },
                {
                    "price": {"total": {"total": "300.00"}},
                    "itineraries": [{"segments": [{"airline": {"iata": "DL"}}, {"airline": {"iata": "UA"}}]}]
                },
                {
                    "price": {"total": {"total": "250.00"}},
                    "itineraries": [{"segments": [{"airline": {"iata": "WN"}}]}]
                }
            ]
            
            stats = calculate_flight_statistics(mock_flights)
            
            expected_stats = {
                "total_results": 3,
                "min_price": 200.0,
                "max_price": 300.0,
                "average_price": 250.0,
                "direct_flights": 2,
                "connecting_flights": 1
            }
            
            stats_match = all(
                stats.get(key) == expected_stats[key] 
                for key in expected_stats.keys()
            )
            
            self.log_test(
                "Statistics Calculation",
                stats_match,
                {
                    "calculated_stats": stats,
                    "expected_stats": expected_stats
                }
            )
            
        except Exception as e:
            self.log_test("Statistics Calculation", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting FlightAPI Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_oneway_flights()
        await self.test_roundtrip_flights()
        await self.test_airport_schedule()
        await self.test_validation_functions()
        await self.test_statistics_calculation()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("flightapi_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: flightapi_test_results.json")

async def main():
    """Main test runner"""
    print("✈️ FlightAPI Test Suite")
    print("Documentation: https://docs.flightapi.io/")
    print()
    
    # Check environment variables
    required_env_vars = ["FLIGHT_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('FLIGHT_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = FlightAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())