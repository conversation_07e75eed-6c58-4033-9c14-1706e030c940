import type { Config } from "tailwindcss";
const config: Config = {
  content: ["./app/**/*.{ts,tsx}", "./src/**/*.{ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // 订阅按钮专用颜色
        myrNavy: "#0B1B47",        // 深海军蓝
        myrYellow: "#FFD200",      // 金黄色
        my: {
          // 核心马来西亚色彩
          primary: "#010066",      // 深海军蓝 - 代表海洋和天空
          secondary: "#FFCC00",    // 金黄色 - 代表财富和繁荣
          accent: "#CC0000",       // 红色 - 代表勇气和活力
          background: "#FAF9F6",   // 象牙白 - 代表沙滩和纯洁
          
          // 扩展马来西亚色彩
          blue: {
            50: "#F0F4FF",         // 浅蓝天空
            100: "#E1E9FF",        // 淡蓝云彩
            200: "#C3D3FF",        // 中蓝海洋
            300: "#A5BDFF",        // 深蓝海水
            400: "#87A7FF",        // 蓝宝石
            500: "#6991FF",        // 马来西亚蓝
            600: "#4B7BFF",        // 深蓝
            700: "#2D65FF",        // 海军蓝
            800: "#0F4FFF",        // 深海军
            900: "#010066",        // 主色调
          },
          
          gold: {
            50: "#FFFDF0",         // 浅金沙滩
            100: "#FFFBE1",        // 淡金阳光
            200: "#FFF7C3",        // 中金麦田
            300: "#FFF3A5",        // 深金丰收
            400: "#FFEF87",        // 金黄
            500: "#FFEB69",        // 马来西亚金
            600: "#FFE74B",        // 深金
            700: "#FFE32D",        // 古金
            800: "#FFDF0F",        // 深古金
            900: "#FFCC00",        // 主色调
          },
          
          red: {
            50: "#FFF0F0",         // 浅红玫瑰
            100: "#FFE1E1",        // 淡红花瓣
            200: "#FFC3C3",        // 中红火焰
            300: "#FFA5A5",        // 深红夕阳
            400: "#FF8787",        // 红宝石
            500: "#FF6969",        // 马来西亚红
            600: "#FF4B4B",        // 深红
            700: "#FF2D2D",        // 古红
            800: "#FF0F0F",        // 深古红
            900: "#CC0000",        // 主色调
          },
          
          green: {
            50: "#F0FFF0",         // 浅绿热带
            100: "#E1FFE1",        // 淡绿丛林
            200: "#C3FFC3",        // 中绿森林
            300: "#A5FFA5",        // 深绿雨林
            400: "#87FF87",        // 绿宝石
            500: "#69FF69",        // 马来西亚绿
            600: "#4BFF4B",        // 深绿
            700: "#2DFF2D",        // 古绿
            800: "#0FFF0F",        // 深古绿
            900: "#28A745",        // 主色调
          },
          
          // 中性色彩
          ink: "#212529",          // 正文/标题
          success: "#28A745",      // 成功绿
          warning: "#FFC107",      // 警告黄
          neutral: {
            50: "#FAFAFA",         // 浅灰
            100: "#F5F5F5",        // 淡灰
            200: "#E5E5E5",        // 中灰
            300: "#D4D4D4",        // 深灰
            400: "#A3A3A3",        // 深中灰
            500: "#737373",        // 中深灰
            600: "#525252",        // 深深灰
            700: "#404040",        // 深灰
            800: "#262626",        // 深深灰
            900: "#171717",        // 最深灰
          }
        }
      },
      fontFamily: {
        display: ["Plus Jakarta Sans", "sans-serif"],
        body: ["Inter", "Noto Sans", "sans-serif"]
      },
      letterSpacing: {
        tighter2: "-0.02em",
        tighter3: "-0.03em"
      },
      lineHeight: {
        snugPlus: "1.15"
      },
      boxShadow: {
        soft: "0px 4px 10px rgba(0,0,0,0.1)",
        malaysia: "0 8px 32px rgba(1, 0, 102, 0.15)",
        gold: "0 4px 20px rgba(255, 204, 0, 0.3)"
      },
      borderRadius: {
        xl2: "1.25rem"
      },
      backgroundImage: {
        'malaysia-gradient': 'linear-gradient(135deg, #010066 0%, #2D65FF 25%, #FFCC00 50%, #FF6969 75%, #28A745 100%)',
        'ocean-gradient': 'linear-gradient(135deg, #010066 0%, #4B7BFF 50%, #87A7FF 100%)',
        'sunset-gradient': 'linear-gradient(135deg, #FFCC00 0%, #FF6969 50%, #FF4B4B 100%)',
        'tropical-gradient': 'linear-gradient(135deg, #28A745 0%, #69FF69 50%, #FFCC00 100%)'
      }
    }
  },
  plugins: []
};
export default config;
