# app/routers/geoapify_content.py
from fastapi import APIRouter, Query, HTTPException, Path
from typing import Optional, List
from datetime import datetime

from app.services.geoapify_service import (
    search_locations,
    search_locations_by_coordinates,
    get_location_details,
    autocomplete_locations,
    extract_location_summary,
    validate_coordinates,
    validate_place_categories
)
from app.models.geoapify_model import (
    LocationSearchResponse,
    LocationDetailsResponse,
    PlaceType,
    GeoapifyError
)

router = APIRouter(prefix="/geoapify", tags=["Geoapify Location Services"])

@router.get("/health")
async def health_check():
    """Health check endpoint for Geoapify API integration"""
    return {
        "status": "healthy",
        "service": "Geoapify Location Services",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "endpoints": ["search", "search-nearby", "details", "autocomplete"]
    }

@router.get("/search")
async def search_locations_endpoint(
    text: str = Query(..., description="Search query text"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results (1-100)"),
    categories: Optional[str] = Query(None, description="Comma-separated list of place categories"),
    bias_lat: Optional[float] = Query(None, description="Latitude to bias results towards"),
    bias_lon: Optional[float] = Query(None, description="Longitude to bias results towards"),
    countries: Optional[str] = Query(None, description="Comma-separated ISO country codes")
):
    """
    Search for locations using text query
    
    - **text**: Search query (e.g., "restaurants in Paris", "hotels near me")
    - **limit**: Maximum number of results to return (1-100)
    - **categories**: Filter by place categories (accommodation, commercial, catering, etc.)
    - **bias_lat/bias_lon**: Coordinates to bias search results towards
    - **countries**: Limit search to specific countries (e.g., "US,CA,GB")
    
    Returns a list of matching locations with essential information.
    """
    try:
        # Validate bias coordinates if provided
        bias_location = None
        if bias_lat is not None and bias_lon is not None:
            if not validate_coordinates(bias_lat, bias_lon):
                raise HTTPException(status_code=400, detail="Invalid bias coordinates")
            bias_location = (bias_lat, bias_lon)
        elif bias_lat is not None or bias_lon is not None:
            raise HTTPException(status_code=400, detail="Both bias_lat and bias_lon must be provided")
        
        # Parse and validate categories
        category_list = None
        if categories:
            category_list = [cat.strip() for cat in categories.split(",")]
            validated_categories = validate_place_categories(category_list)
            if not validated_categories:
                raise HTTPException(status_code=400, detail="No valid categories provided")
            category_list = validated_categories
        
        # Parse country codes
        country_list = None
        if countries:
            country_list = [country.strip().upper() for country in countries.split(",")]
        
        # Search locations
        response = await search_locations(
            text=text,
            limit=limit,
            categories=category_list,
            bias_location=bias_location,
            country_codes=country_list
        )
        
        # Add summary information for each location
        if "features" in response:
            for feature in response["features"]:
                # Filter properties to keep only useful information
                if "properties" in feature:
                    useful_props = {}
                    props = feature["properties"]
                    
                    # Essential fields
                    for field in ["name", "formatted", "address_line1", "address_line2", 
                                "city", "state", "country", "country_code", "postcode",
                                "place_id", "categories"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    # Contact fields
                    for field in ["phone", "website"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    # Business fields
                    for field in ["opening_hours", "rating"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    feature["properties"] = useful_props
        
        return response
        
    except GeoapifyError as e:
        if e.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key")
        elif e.status_code == 403:
            raise HTTPException(status_code=403, detail="API access forbidden")
        elif e.status_code == 429:
            raise HTTPException(status_code=429, detail="API rate limit exceeded")
        else:
            raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/search-nearby")
async def search_nearby_locations_endpoint(
    lat: float = Query(..., description="Latitude"),
    lon: float = Query(..., description="Longitude"),
    radius: int = Query(1000, ge=100, le=50000, description="Search radius in meters (100-50000)"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results (1-100)"),
    categories: Optional[str] = Query(None, description="Comma-separated list of place categories")
):
    """
    Search for locations near specific coordinates
    
    - **lat**: Latitude coordinate
    - **lon**: Longitude coordinate  
    - **radius**: Search radius in meters (100-50000)
    - **limit**: Maximum number of results to return (1-100)
    - **categories**: Filter by place categories (accommodation, commercial, catering, etc.)
    
    Returns locations within the specified radius of the given coordinates.
    """
    try:
        # Validate coordinates
        if not validate_coordinates(lat, lon):
            raise HTTPException(status_code=400, detail="Invalid coordinates")
        
        # Parse and validate categories
        category_list = None
        if categories:
            category_list = [cat.strip() for cat in categories.split(",")]
            validated_categories = validate_place_categories(category_list)
            if not validated_categories:
                raise HTTPException(status_code=400, detail="No valid categories provided")
            category_list = validated_categories
        
        # Search nearby locations
        response = await search_locations_by_coordinates(
            lat=lat,
            lon=lon,
            radius=radius,
            limit=limit,
            categories=category_list
        )
        
        # Filter response properties
        if "features" in response:
            for feature in response["features"]:
                if "properties" in feature:
                    useful_props = {}
                    props = feature["properties"]
                    
                    # Keep only essential fields
                    for field in ["name", "formatted", "address_line1", "city", 
                                "country", "place_id", "categories", "phone", 
                                "website", "rating"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    feature["properties"] = useful_props
        
        return response
        
    except GeoapifyError as e:
        if e.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key")
        elif e.status_code == 429:
            raise HTTPException(status_code=429, detail="API rate limit exceeded")
        else:
            raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/details/{place_id}")
async def get_location_details_endpoint(
    place_id: str = Path(..., description="Unique place identifier")
):
    """
    Get detailed information about a specific place
    
    - **place_id**: Unique identifier for the place (obtained from search results)
    
    Returns comprehensive details about the specified location including:
    - Complete address information
    - Contact details (phone, website, email)
    - Business information (hours, rating, description)
    - Categories and classification
    """
    try:
        # Get location details
        response = await get_location_details(place_id)
        
        # Filter response to keep only useful information
        if "features" in response:
            for feature in response["features"]:
                if "properties" in feature:
                    useful_props = {}
                    props = feature["properties"]
                    
                    # Address fields
                    for field in ["name", "formatted", "address_line1", "address_line2",
                                "city", "state", "country", "country_code", "postcode"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    # Identification
                    for field in ["place_id", "categories"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    # Contact information
                    for field in ["phone", "website", "email"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    # Business details
                    for field in ["opening_hours", "rating", "description", "brand", "operator"]:
                        if field in props and props[field]:
                            useful_props[field] = props[field]
                    
                    feature["properties"] = useful_props
        
        return response
        
    except GeoapifyError as e:
        if e.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key")
        elif e.status_code == 404:
            raise HTTPException(status_code=404, detail="Place not found")
        elif e.status_code == 429:
            raise HTTPException(status_code=429, detail="API rate limit exceeded")
        else:
            raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/autocomplete")
async def autocomplete_locations_endpoint(
    text: str = Query(..., description="Partial search text"),
    limit: int = Query(5, ge=1, le=20, description="Maximum number of suggestions (1-20)"),
    bias_lat: Optional[float] = Query(None, description="Latitude to bias results towards"),
    bias_lon: Optional[float] = Query(None, description="Longitude to bias results towards"),
    countries: Optional[str] = Query(None, description="Comma-separated ISO country codes")
):
    """
    Get autocomplete suggestions for location search
    
    - **text**: Partial search text (e.g., "New Y", "Lond")
    - **limit**: Maximum number of suggestions to return (1-20)
    - **bias_lat/bias_lon**: Coordinates to bias suggestions towards
    - **countries**: Limit suggestions to specific countries
    
    Returns autocomplete suggestions for location names and addresses.
    """
    try:
        # Validate bias coordinates if provided
        bias_location = None
        if bias_lat is not None and bias_lon is not None:
            if not validate_coordinates(bias_lat, bias_lon):
                raise HTTPException(status_code=400, detail="Invalid bias coordinates")
            bias_location = (bias_lat, bias_lon)
        elif bias_lat is not None or bias_lon is not None:
            raise HTTPException(status_code=400, detail="Both bias_lat and bias_lon must be provided")
        
        # Parse country codes
        country_list = None
        if countries:
            country_list = [country.strip().upper() for country in countries.split(",")]
        
        # Get autocomplete suggestions
        response = await autocomplete_locations(
            text=text,
            limit=limit,
            bias_location=bias_location,
            country_codes=country_list
        )
        
        # Simplify response for autocomplete
        if "features" in response:
            for feature in response["features"]:
                if "properties" in feature:
                    props = feature["properties"]
                    simplified_props = {}
                    
                    # Keep only essential fields for autocomplete
                    for field in ["name", "formatted", "city", "country", "place_id"]:
                        if field in props and props[field]:
                            simplified_props[field] = props[field]
                    
                    feature["properties"] = simplified_props
        
        return response
        
    except GeoapifyError as e:
        if e.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key")
        elif e.status_code == 429:
            raise HTTPException(status_code=429, detail="API rate limit exceeded")
        else:
            raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/categories")
async def get_available_categories():
    """
    Get list of available place categories for filtering
    
    Returns all available place categories that can be used in search filters.
    """
    return {
        "categories": [
            {
                "value": category.value,
                "name": category.value.replace("_", " ").title()
            }
            for category in PlaceType
        ]
    }