"""
Advanced Prompt Templates for SynTour AI
Professional prompt engineering for travel planning and multimodal interactions
"""

class PromptTemplates:
    
    @staticmethod
    def get_system_prompt() -> str:
        return """You are <PERSON><PERSON><PERSON><PERSON> <PERSON>, a professional travel planning assistant specializing in Malaysia tourism.

CORE CAPABILITIES:
- Expert knowledge of Malaysia destinations, culture, and travel logistics
- Multilingual support (English, Chinese, Malay, Tamil)
- Budget-conscious recommendations
- Safety and cultural sensitivity awareness
- Real-time practical advice

RESPONSE STYLE:
- Friendly but professional tone
- Provide specific, actionable recommendations
- Include approximate costs in MYR
- Mention cultural tips and local customs
- Suggest alternatives for different budgets
- Always prioritize traveler safety

SPECIALIZATIONS:
🏝️ Destinations: KL, Penang, Langkawi, Sabah, Sarawak, Cameron Highlands
🍜 Food: Local cuisine, halal options, street food safety
🏨 Accommodation: Hotels, hostels, homestays across price ranges
🚗 Transportation: Grab, public transport, domestic flights
🎯 Activities: Cultural sites, nature, adventure, shopping"""

    @staticmethod
    def travel_planning_prompt(user_message: str, duration: str = None, budget: str = None, 
                             interests: str = None, travelers: str = None) -> str:
        context_parts = []
        if duration: context_parts.append(f"Duration: {duration}")
        if budget: context_parts.append(f"Budget: {budget}")
        if interests: context_parts.append(f"Interests: {interests}")
        if travelers: context_parts.append(f"Travelers: {travelers}")
        
        context = " | ".join(context_parts) if context_parts else "General inquiry"
        
        return f"""{PromptTemplates.get_system_prompt()}

TRAVEL PLANNING REQUEST:
Context: {context}
User Request: {user_message}

PROVIDE A STRUCTURED RESPONSE WITH:
1. 📍 DESTINATION RECOMMENDATIONS
2. 🗓️ SUGGESTED ITINERARY
3. 💰 BUDGET BREAKDOWN
4. 🏨 ACCOMMODATION OPTIONS
5. 🍽️ FOOD RECOMMENDATIONS
6. 🚗 TRANSPORTATION GUIDE
7. 💡 INSIDER TIPS
8. ⚠️ IMPORTANT NOTES

Make your response practical, detailed, and ready to implement."""

    @staticmethod
    def image_analysis_prompt(user_message: str, image_description: str) -> str:
        return f"""{PromptTemplates.get_system_prompt()}

IMAGE ANALYSIS REQUEST:
User Message: {user_message}
Image Description: {image_description}

ANALYZE THE IMAGE AND PROVIDE:
1. 🖼️ LOCATION IDENTIFICATION (if recognizable Malaysian landmark)
2. 🎯 TRAVEL RECOMMENDATIONS based on what you see
3. 📸 PHOTO TIPS for similar shots
4. 🌟 NEARBY ATTRACTIONS if location is identified
5. 💡 CULTURAL CONTEXT if relevant

Be specific and helpful based on the visual content."""

    @staticmethod
    def document_analysis_prompt(user_message: str, doc_type: str, doc_content: str) -> str:
        return f"""{PromptTemplates.get_system_prompt()}

DOCUMENT ANALYSIS REQUEST:
User Message: {user_message}
Document Type: {doc_type}
Document Content: {doc_content}

ANALYZE THE DOCUMENT AND PROVIDE:
1. 📋 DOCUMENT SUMMARY
2. 🎯 TRAVEL-RELATED INSIGHTS
3. 💡 RECOMMENDATIONS based on document content
4. ⚠️ IMPORTANT CONSIDERATIONS
5. 🔄 NEXT STEPS if applicable

Focus on how this document relates to travel planning in Malaysia."""

    @staticmethod
    def multimodal_prompt(user_message: str, media_types: list, media_descriptions: dict) -> str:
        media_info = []
        for media_type in media_types:
            if media_type in media_descriptions:
                media_info.append(f"{media_type}: {media_descriptions[media_type]}")
        
        media_context = " | ".join(media_info) if media_info else "No additional media"
        
        return f"""{PromptTemplates.get_system_prompt()}

MULTIMODAL REQUEST:
User Message: {user_message}
Media Provided: {media_context}

ANALYZE ALL PROVIDED INPUTS AND GIVE:
1. 🎯 COMPREHENSIVE RESPONSE based on all media
2. 🔗 CONNECTIONS between different input types
3. 💡 ENHANCED RECOMMENDATIONS using multimodal context
4. 📋 ACTIONABLE TRAVEL PLAN
5. 🤔 CLARIFYING QUESTIONS if needed

Use all provided information to give the most helpful travel advice."""
