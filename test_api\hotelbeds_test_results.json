{"summary": {"total": 13, "passed": 12, "failed": 1, "success_rate": 92.3076923076923}, "results": [{"test": "Hotels List - Basic Request", "success": true, "timestamp": "2025-08-30T00:21:25.836494", "details": {"hotels_count": 10, "country": "MY"}}, {"test": "Hotel Model Structure", "success": true, "timestamp": "2025-08-30T00:21:25.836494", "details": {"missing_fields": [], "available_fields": ["code"], "sample_hotel_code": 59456}}, {"test": "Hotels List - Detailed Fields", "success": true, "timestamp": "2025-08-30T00:21:26.299524", "details": {"country": "SG", "fields": "all"}}, {"test": "Hotel Details - Structure", "success": true, "timestamp": "2025-08-30T00:21:27.133375", "details": {"hotel_code": 59456, "hotel_name": "Dorsett Kuala Lumpur", "has_facilities": true, "has_rooms": true, "available_fields": ["code", "name", "description", "country", "state", "destination", "zone", "coordinates", "category", "categoryGroup"]}}, {"test": "Location APIs", "success": false, "timestamp": "2025-08-30T00:21:27.740848", "details": {"error": ""}}, {"test": "Accommodations API", "success": true, "timestamp": "2025-08-30T00:21:28.047485", "details": {"response_type": "dict"}}, {"test": "Categories API", "success": true, "timestamp": "2025-08-30T00:21:28.270510", "details": {"response_type": "dict"}}, {"test": "Chains API", "success": true, "timestamp": "2025-08-30T00:21:28.560297", "details": {"response_type": "dict"}}, {"test": "Facilities API", "success": true, "timestamp": "2025-08-30T00:21:29.103198", "details": {"response_type": "dict"}}, {"test": "Pagination - Different Page Sizes", "success": true, "timestamp": "2025-08-30T00:21:29.637441", "details": {"small_page": 5, "large_page": 20}}, {"test": "Pagination - Metada<PERSON>", "success": true, "timestamp": "2025-08-30T00:21:29.638296", "details": {"total": 2583, "from": 1, "to": 20}}, {"test": "Error <PERSON> - Invalid Hotel Code", "success": true, "timestamp": "2025-08-30T00:21:29.901979", "details": {"response": "No hotel data returned"}}, {"test": "<PERSON>rro<PERSON> - Invalid Country", "success": true, "timestamp": "2025-08-30T00:21:30.168577", "details": {"response": "Handled gracefully"}}]}