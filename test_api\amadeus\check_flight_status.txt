Link to documentation:
1. https://developers.amadeus.com/self-service/apis-docs/guides/developer-guides/resources/flights/#check-the-flight-status
2. https://developers.amadeus.com/self-service/category/flights/api-doc/on-demand-flight-status/api-reference

Check the flight status
The On-Demand Flight Status API provides real-time flight schedule data including up-to-date departure and arrival times, terminal and gate information, flight duration and real-time delay status.

To get this information, the only mandatory parameters to send a query are the IATA carrier code, flight number and scheduled departure date, and you'll be up to date about your flight schedule. For example, checking the Iberia flight 532 on 23 March 2022:


https://test.api.amadeus.com/v2/schedule/flights?carrierCode=IB&flightNumber=532&scheduledDepartureDate=2022-03-23
If the flight changes and the carrier assigns a prefix to the flight number to indicate the change, you can specify it in the query using the additional one-letter operationalSuffix parameter:


https://test.api.amadeus.com/v2/schedule/flights?carrierCode=IB&flightNumber=532&scheduledDepartureDate=2021-03-23&operationalSuffix=A
The example response looks as follows:


{
  "meta": {
    "count": 1,
    "links": {
      "self": "https://test.api.amadeus.com/v2/schedule/flights?carrierCode=AZ&flightNumber=319&scheduledDepartureDate=2021-03-13"
    }
  },
  "data": [
    {
      "type": "DatedFlight",
      "scheduledDepartureDate": "2021-03-13",
      "flightDesignator": {
        "carrierCode": "AZ",
        "flightNumber": 319
      },
      "flightPoints": [
        {
          "iataCode": "CDG",
          "departure": {
            "timings": [
              {
                "qualifier": "STD",
                "value": "2021-03-13T11:10+01:00"
              }
            ]
          }
        },
        {
          "iataCode": "FCO",
          "arrival": {
            "timings": [
              {
                "qualifier": "STA",
                "value": "2021-03-13T13:15+01:00"
              }
            ]
          }
        }
      ],
      "segments": [
        {
          "boardPointIataCode": "CDG",
          "offPointIataCode": "FCO",
          "scheduledSegmentDuration": "PT2H5M"
        }
      ],
      "legs": [
        {
          "boardPointIataCode": "CDG",
          "offPointIataCode": "FCO",
          "aircraftEquipment": {
            "aircraftType": "32S"
          },
          "scheduledLegDuration": "PT2H5M"
        }
      ]
    }
  ]
}