"use client";
import { useState, useRef, useEffect, useCallback } from "react";

interface VerticalDividerHandleProps {
  onHeightChange: (height: number) => void;
  maxHeight: number;
  initialHeight: number;
  containerTop: number;
  containerHeight: number;
}

export default function VerticalDividerHandle({
  onHeightChange,
  maxHeight,
  initialHeight,
  containerTop,
  containerHeight
}: VerticalDividerHandleProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [handlePosition, setHandlePosition] = useState(0);
  const dividerRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);

  // 计算把手位置对应的面板高度
  const calculatePanelHeight = useCallback((position: number) => {
    const ratio = 1 - (position / containerHeight);
    return Math.max(0, Math.min(maxHeight, ratio * maxHeight));
  }, [maxHeight, containerHeight]);

  // 根据面板高度计算把手位置
  const calculateHandlePosition = useCallback((panelHeight: number) => {
    const ratio = 1 - (panelHeight / maxHeight);
    return ratio * containerHeight;
  }, [maxHeight, containerHeight]);

  // 初始化把手位置
  useEffect(() => {
    const position = calculateHandlePosition(initialHeight);
    setHandlePosition(position);
  }, [initialHeight, calculateHandlePosition]);

  // 处理鼠标拖拽
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const startY = e.clientY;
    const startPosition = handlePosition;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = e.clientY - startY;
      const newPosition = Math.max(0, Math.min(containerHeight, startPosition + deltaY));
      setHandlePosition(newPosition);
      
      const newHeight = calculatePanelHeight(newPosition);
      onHeightChange(newHeight);
      
      // 埋点
      console.debug('ui.historyDivider.drag', { height: newHeight, position: newPosition });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [handlePosition, containerHeight, calculatePanelHeight, onHeightChange]);

  // 处理触摸拖拽
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const startY = e.touches[0].clientY;
    const startPosition = handlePosition;

    const handleTouchMove = (e: TouchEvent) => {
      const deltaY = e.touches[0].clientY - startY;
      const newPosition = Math.max(0, Math.min(containerHeight, startPosition + deltaY));
      setHandlePosition(newPosition);
      
      const newHeight = calculatePanelHeight(newPosition);
      onHeightChange(newHeight);
    };

    const handleTouchEnd = () => {
      setIsDragging(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  }, [handlePosition, containerHeight, calculatePanelHeight, onHeightChange]);

  // 双击切换档位
  const handleDoubleClick = useCallback(() => {
    const currentHeight = calculatePanelHeight(handlePosition);
    const minHeight = 0;
    const midHeight = maxHeight * 0.3;
    const maxFullHeight = maxHeight * 0.5;

    let targetHeight: number;
    if (currentHeight <= minHeight + 10) {
      targetHeight = midHeight;
    } else if (currentHeight <= midHeight + 10) {
      targetHeight = maxFullHeight;
    } else {
      targetHeight = minHeight;
    }

    const newPosition = calculateHandlePosition(targetHeight);
    setHandlePosition(newPosition);
    onHeightChange(targetHeight);

    console.debug('ui.historyDivider.doubleClick', { height: targetHeight });
  }, [handlePosition, maxHeight, calculatePanelHeight, calculateHandlePosition, onHeightChange]);

  // 键盘导航
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    const step = 24;
    let newPosition = handlePosition;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        newPosition = Math.max(0, handlePosition - step);
        break;
      case 'ArrowDown':
        e.preventDefault();
        newPosition = Math.min(containerHeight, handlePosition + step);
        break;
      case 'Home':
        e.preventDefault();
        newPosition = 0;
        break;
      case 'End':
        e.preventDefault();
        newPosition = containerHeight;
        break;
      default:
        return;
    }

    setHandlePosition(newPosition);
    const newHeight = calculatePanelHeight(newPosition);
    onHeightChange(newHeight);
  }, [handlePosition, containerHeight, calculatePanelHeight, onHeightChange]);

  const currentHeight = calculatePanelHeight(handlePosition);
  const valueMin = 0;
  const valueMax = maxHeight;

  return (
    <div
      ref={dividerRef}
      className="fixed left-20 z-[70] w-1 transition-all duration-200"
      style={{
        top: containerTop,
        height: containerHeight,
        background: `linear-gradient(to bottom, rgba(11, 27, 71, 0.25), rgba(255, 210, 0, 0.3))`
      }}
    >
      {/* 可拖拽把手 */}
      <div
        ref={handleRef}
        className={`absolute left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg cursor-ns-resize transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-myrYellow/50 ${
          isDragging ? 'scale-110 shadow-xl' : 'hover:scale-105'
        } ${
          // 移动端更大的触控目标
          'sm:w-8 sm:h-2 w-10 h-6'
        }`}
        style={{
          top: handlePosition - 8
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onDoubleClick={handleDoubleClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="separator"
        aria-orientation="vertical"
        aria-valuemin={valueMin}
        aria-valuemax={valueMax}
        aria-valuenow={currentHeight}
        aria-label="Resize history panel"
        title="Drag to resize history panel, double-click to toggle between sizes"
      >
        {/* 把手的视觉指示器 */}
        <div className="w-full h-full bg-gradient-to-r from-myrNavy/40 to-myrYellow/40 rounded-full flex items-center justify-center">
          <div className="hidden sm:block w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="sm:hidden w-2 h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
