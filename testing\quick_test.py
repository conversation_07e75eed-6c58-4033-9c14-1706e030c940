#!/usr/bin/env python3
"""
快速测试脚本 - 验证关键bug修复
基于网络资源研究和Google Cloud官方文档的修复验证
"""

import requests
import json
import time
import websocket
import threading

API_BASE_URL = "http://localhost:8002"
WS_BASE_URL = "ws://localhost:8002"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_chat_api():
    """测试聊天API"""
    print("🔍 测试聊天API...")
    try:
        # 测试正常请求
        payload = {
            "message": "Hello, this is a test message",
            "context": "Testing context",
            "user_preferences": {}
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/ai/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ 聊天API正常工作")
                print(f"AI响应: {data.get('response', 'No response')[:100]}...")
                return True
            else:
                print(f"❌ 聊天API返回错误: {data.get('error')}")
                return False
        elif response.status_code == 422:
            print("❌ 422错误仍然存在")
            print(f"错误详情: {response.text}")
            return False
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天API测试异常: {e}")
        return False

def test_empty_message():
    """测试空消息验证"""
    print("🔍 测试空消息验证...")
    try:
        payload = {
            "message": "",
            "context": "",
            "user_preferences": {}
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/ai/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if not data.get("success") and "empty" in data.get("error", "").lower():
                print("✅ 空消息验证正常工作")
                return True
            else:
                print(f"❌ 空消息验证失败: {data}")
                return False
        else:
            print(f"❌ 空消息测试意外状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 空消息测试异常: {e}")
        return False

def test_websocket_connection():
    """测试WebSocket连接"""
    print("🔍 测试WebSocket连接...")
    try:
        client_id = f"test_client_{int(time.time())}"
        ws_url = f"{WS_BASE_URL}/ws/speech/{client_id}"

        def on_message(ws, message):
            print(f"收到WebSocket消息: {message}")

        def on_error(ws, error):
            print(f"WebSocket错误: {error}")

        def on_close(ws, close_status_code, close_msg):
            print(f"WebSocket关闭: {close_status_code} - {close_msg}")

        def on_open(ws):
            print("✅ WebSocket连接成功建立")
            # 发送测试音频数据
            test_audio = b'\x00' * 1024  # 1KB的静音数据
            ws.send(test_audio, websocket.ABNF.OPCODE_BINARY)
            time.sleep(2)
            ws.close()

        ws = websocket.WebSocketApp(ws_url,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)

        # 运行WebSocket连接（超时5秒）
        ws.run_forever(timeout=5)
        return True

    except Exception as e:
        print(f"❌ WebSocket测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始快速测试...")
    print("基于Google Cloud Speech API官方文档的修复验证")
    print("=" * 60)

    results = []

    # 测试健康检查
    results.append(("健康检查", test_health()))

    # 等待一下
    time.sleep(1)

    # 测试聊天API
    results.append(("聊天API", test_chat_api()))

    # 测试空消息验证
    results.append(("空消息验证", test_empty_message()))

    # 测试WebSocket连接
    results.append(("WebSocket连接", test_websocket_connection()))
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果:")

    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False

    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！关键bug已修复。")
        print("✅ 语音识别API调用已修复")
        print("✅ WebSocket连接稳定")
        print("✅ 聊天API正常工作")
        print("✅ 错误处理改进")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        print("📋 修复要点:")
        print("  1. 确保Google Cloud凭据正确配置")
        print("  2. 检查Speech-to-Text API是否启用")
        print("  3. 验证Vertex AI端点配置")
        print("  4. 确认所有环境变量设置正确")

    return all_passed

if __name__ == "__main__":
    print("快速测试脚本 - 验证关键bug修复")
    print("请确保后端服务器正在运行...")
    
    result = main()
    exit(0 if result else 1)
