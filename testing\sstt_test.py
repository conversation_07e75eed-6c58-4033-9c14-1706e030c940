# backend/main.py
import os
from fastapi import FastAP<PERSON>, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from google.cloud import speech
from google.cloud.speech import StreamingRecognizeRequest, StreamingRecognitionConfig, RecognitionConfig
import asyncio

# Project B 的 Speech-to-Text 服务账号
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = r"C:\Users\<USER>\Desktop\TRAVEL_AI\silver-course-470107-h0-de21f66e46dc.json"

app = FastAPI()

# 允许 React 前端跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可限制成前端地址
    allow_methods=["*"],
    allow_headers=["*"],
)

client = speech.SpeechClient()

# 候选语言列表（可扩展）
CANDIDATE_LANGUAGES = [
    "en-US", "zh-CN", "ja-JP", "fr-FR", "es-ES",
    "de-DE", "ko-KR", "ru-RU", "it-IT", "pt-BR",
    "id-ID", "ms-MY", "ta-IN", "ta-MY", "th-TH",
    "tr-TR", "uk-UA", "uz-UZ", "vi-VN", "sv-SE"
]

@app.websocket("/ws/speech")
async def websocket_speech(websocket: WebSocket):
    await websocket.accept()

    # RecognitionConfig：LINEAR16 PCM 16-bit 16kHz 单声道
    config = RecognitionConfig(
        encoding=RecognitionConfig.AudioEncoding.LINEAR16,
        sample_rate_hertz=16000,
        language_code="en-US",  # 默认语言
        alternative_language_codes=CANDIDATE_LANGUAGES
    )

    streaming_config = StreamingRecognitionConfig(
        config=config,
        interim_results=True
    )

    # 音频生成器
    async def audio_generator():
        while True:
            try:
                chunk = await websocket.receive_bytes()
                yield StreamingRecognizeRequest(audio_content=chunk)
            except Exception:
                break

    # 调用 Google Cloud StreamingRecognize
    responses = client.streaming_recognize(streaming_config, audio_generator())

    async for response in responses:
        for result in response.results:
            transcript = result.alternatives[0].transcript
            detected_lang = result.language_code
            await websocket.send_json({
                "transcript": transcript,
                "is_final": result.is_final,
                "detected_language": detected_lang
            })
