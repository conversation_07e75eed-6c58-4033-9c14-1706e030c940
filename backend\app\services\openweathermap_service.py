# app/services/openweathermap_service.py
import os
import httpx
import logging
from typing import Optional, Dict, Any, List
from fastapi import HTTPException
import json
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

# OpenWeatherMap API Configuration
OPENWEATHERMAP_BASE_URL = "https://api.openweathermap.org/data/2.5"
OPENWEATHERMAP_GEO_URL = "https://api.openweathermap.org/geo/1.0"
OPENWEATHERMAP_ONECALL_URL = "https://api.openweathermap.org/data/3.0"

def get_openweathermap_credentials():
    """Get OpenWeatherMap API credentials from environment variables"""
    api_key = os.getenv('OPEN_WEATHER_MAP_API_KEY')
    
    if not api_key:
        raise ValueError("OPEN_WEATHER_MAP_API_KEY environment variable must be set")
    
    return api_key

async def openweathermap_request(
    base_url: str,
    endpoint: str, 
    params: Optional[Dict[str, Any]] = None
) -> dict:
    """Generic OpenWeatherMap API request with error handling"""
    try:
        api_key = get_openweathermap_credentials()
        
        # Prepare parameters
        request_params = params or {}
        request_params['appid'] = api_key
        
        # Prepare headers
        headers = {
            "Accept": "application/json",
            "User-Agent": "SynTour-Travel-App/1.0"
        }
        
        url = f"{base_url}{endpoint}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info(f"Making request to: {url}")
            
            response = await client.get(url, headers=headers, params=request_params)
            
            # Handle different response status codes
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    logger.error("Invalid JSON response from OpenWeatherMap")
                    raise HTTPException(status_code=502, detail="Invalid response from weather service")
            elif response.status_code == 401:
                logger.error("OpenWeatherMap authentication failed")
                raise HTTPException(status_code=401, detail="Invalid API key")
            elif response.status_code == 404:
                logger.error("OpenWeatherMap location not found")
                raise HTTPException(status_code=404, detail="Location not found")
            elif response.status_code == 429:
                logger.error("OpenWeatherMap rate limit exceeded")
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            elif response.status_code == 500:
                logger.error("OpenWeatherMap internal server error")
                raise HTTPException(status_code=500, detail="Weather service error")
            else:
                try:
                    error_data = response.json()
                    error_message = error_data.get("message", "Unknown error")
                except:
                    error_message = response.text
                
                logger.error(f"OpenWeatherMap API error: {response.status_code} - {error_message}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Weather API error: {error_message}"
                )
                
    except httpx.TimeoutException:
        logger.error("Request timeout to OpenWeatherMap API")
        raise HTTPException(status_code=408, detail="Request timeout")
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise HTTPException(status_code=503, detail="Weather service unavailable")
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Current Weather API functions
async def get_current_weather_by_city(
    city_name: str,
    state_code: Optional[str] = None,
    country_code: Optional[str] = None,
    units: str = "metric",
    lang: str = "en"
) -> dict:
    """Get current weather by city name"""
    # Build query string
    query_parts = [city_name]
    if state_code:
        query_parts.append(state_code)
    if country_code:
        query_parts.append(country_code)
    
    query = ",".join(query_parts)
    
    params = {
        "q": query,
        "units": units,
        "lang": lang
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/weather", params)

async def get_current_weather_by_coordinates(
    lat: float,
    lon: float,
    units: str = "metric",
    lang: str = "en"
) -> dict:
    """Get current weather by coordinates"""
    params = {
        "lat": lat,
        "lon": lon,
        "units": units,
        "lang": lang
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/weather", params)

async def get_current_weather_by_city_id(
    city_id: int,
    units: str = "metric",
    lang: str = "en"
) -> dict:
    """Get current weather by city ID"""
    params = {
        "id": city_id,
        "units": units,
        "lang": lang
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/weather", params)

async def get_current_weather_by_zip(
    zip_code: str,
    country_code: Optional[str] = None,
    units: str = "metric",
    lang: str = "en"
) -> dict:
    """Get current weather by ZIP code"""
    # Build ZIP query
    if country_code:
        zip_query = f"{zip_code},{country_code}"
    else:
        zip_query = zip_code
    
    params = {
        "zip": zip_query,
        "units": units,
        "lang": lang
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/weather", params)

# Geocoding API functions
async def get_coordinates_by_city(
    city_name: str,
    state_code: Optional[str] = None,
    country_code: Optional[str] = None,
    limit: int = 5
) -> dict:
    """Get coordinates by city name using Geocoding API"""
    # Build query string
    query_parts = [city_name]
    if state_code:
        query_parts.append(state_code)
    if country_code:
        query_parts.append(country_code)
    
    query = ",".join(query_parts)
    
    params = {
        "q": query,
        "limit": limit
    }
    
    return await openweathermap_request(OPENWEATHERMAP_GEO_URL, "/direct", params)

async def get_city_by_coordinates(
    lat: float,
    lon: float,
    limit: int = 5
) -> dict:
    """Get city name by coordinates using Reverse Geocoding API"""
    params = {
        "lat": lat,
        "lon": lon,
        "limit": limit
    }
    
    return await openweathermap_request(OPENWEATHERMAP_GEO_URL, "/reverse", params)

# Weather Forecast API functions
async def get_5day_forecast(
    lat: Optional[float] = None,
    lon: Optional[float] = None,
    city_name: Optional[str] = None,
    city_id: Optional[int] = None,
    zip_code: Optional[str] = None,
    country_code: Optional[str] = None,
    units: str = "metric",
    lang: str = "en",
    cnt: Optional[int] = None
) -> dict:
    """Get 5 day weather forecast"""
    params = {
        "units": units,
        "lang": lang
    }
    
    # Add location parameter
    if lat is not None and lon is not None:
        params["lat"] = lat
        params["lon"] = lon
    elif city_name:
        query_parts = [city_name]
        if country_code:
            query_parts.append(country_code)
        params["q"] = ",".join(query_parts)
    elif city_id:
        params["id"] = city_id
    elif zip_code:
        if country_code:
            params["zip"] = f"{zip_code},{country_code}"
        else:
            params["zip"] = zip_code
    else:
        raise ValueError("Must provide either coordinates, city name, city ID, or ZIP code")
    
    if cnt:
        params["cnt"] = cnt
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/forecast", params)

# Air Pollution API functions
async def get_current_air_pollution(
    lat: float,
    lon: float
) -> dict:
    """Get current air pollution data"""
    params = {
        "lat": lat,
        "lon": lon
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/air_pollution", params)

async def get_air_pollution_forecast(
    lat: float,
    lon: float
) -> dict:
    """Get air pollution forecast"""
    params = {
        "lat": lat,
        "lon": lon
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/air_pollution/forecast", params)

async def get_historical_air_pollution(
    lat: float,
    lon: float,
    start: int,
    end: int
) -> dict:
    """Get historical air pollution data"""
    params = {
        "lat": lat,
        "lon": lon,
        "start": start,
        "end": end
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/air_pollution/history", params)

# UV Index API functions
async def get_current_uv_index(
    lat: float,
    lon: float
) -> dict:
    """Get current UV index"""
    params = {
        "lat": lat,
        "lon": lon
    }
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/uvi", params)

async def get_uv_index_forecast(
    lat: float,
    lon: float,
    cnt: Optional[int] = None
) -> dict:
    """Get UV index forecast"""
    params = {
        "lat": lat,
        "lon": lon
    }
    
    if cnt:
        params["cnt"] = cnt
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/uvi/forecast", params)

async def get_historical_uv_index(
    lat: float,
    lon: float,
    start: int,
    end: int,
    cnt: Optional[int] = None
) -> dict:
    """Get historical UV index data"""
    params = {
        "lat": lat,
        "lon": lon,
        "start": start,
        "end": end
    }
    
    if cnt:
        params["cnt"] = cnt
    
    return await openweathermap_request(OPENWEATHERMAP_BASE_URL, "/uvi/history", params)

# Utility functions
def kelvin_to_celsius(kelvin: float) -> float:
    """Convert Kelvin to Celsius"""
    return kelvin - 273.15

def kelvin_to_fahrenheit(kelvin: float) -> float:
    """Convert Kelvin to Fahrenheit"""
    return (kelvin - 273.15) * 9/5 + 32

def celsius_to_fahrenheit(celsius: float) -> float:
    """Convert Celsius to Fahrenheit"""
    return celsius * 9/5 + 32

def fahrenheit_to_celsius(fahrenheit: float) -> float:
    """Convert Fahrenheit to Celsius"""
    return (fahrenheit - 32) * 5/9

def meters_per_second_to_mph(mps: float) -> float:
    """Convert meters per second to miles per hour"""
    return mps * 2.237

def meters_per_second_to_kmh(mps: float) -> float:
    """Convert meters per second to kilometers per hour"""
    return mps * 3.6

def unix_to_datetime(unix_timestamp: int, timezone_offset: int = 0) -> datetime:
    """Convert Unix timestamp to datetime with timezone offset"""
    return datetime.fromtimestamp(unix_timestamp + timezone_offset, tz=timezone.utc)

def get_weather_icon_url(icon_code: str, size: str = "2x") -> str:
    """Get weather icon URL"""
    return f"https://openweathermap.org/img/wn/{icon_code}@{size}.png"

def calculate_heat_index(temperature_f: float, humidity: int) -> float:
    """Calculate heat index in Fahrenheit"""
    if temperature_f < 80:
        return temperature_f
    
    # Heat index formula
    hi = (
        -42.379 +
        2.04901523 * temperature_f +
        10.14333127 * humidity -
        0.22475541 * temperature_f * humidity -
        6.83783e-3 * temperature_f**2 -
        5.481717e-2 * humidity**2 +
        1.22874e-3 * temperature_f**2 * humidity +
        8.5282e-4 * temperature_f * humidity**2 -
        1.99e-6 * temperature_f**2 * humidity**2
    )
    
    return hi

def calculate_wind_chill(temperature_f: float, wind_speed_mph: float) -> float:
    """Calculate wind chill in Fahrenheit"""
    if temperature_f > 50 or wind_speed_mph < 3:
        return temperature_f
    
    # Wind chill formula
    wc = (
        35.74 +
        0.6215 * temperature_f -
        35.75 * (wind_speed_mph ** 0.16) +
        0.4275 * temperature_f * (wind_speed_mph ** 0.16)
    )
    
    return wc

def get_weather_severity_level(weather_id: int) -> str:
    """Get weather severity level based on weather condition ID"""
    if 200 <= weather_id <= 232:  # Thunderstorm
        return "severe"
    elif 300 <= weather_id <= 321:  # Drizzle
        return "light"
    elif 500 <= weather_id <= 531:  # Rain
        if weather_id in [502, 503, 504, 511, 522, 531]:
            return "moderate"
        else:
            return "light"
    elif 600 <= weather_id <= 622:  # Snow
        if weather_id in [602, 612, 613, 615, 616, 620, 621, 622]:
            return "moderate"
        else:
            return "light"
    elif 701 <= weather_id <= 781:  # Atmosphere
        if weather_id == 781:  # Tornado
            return "severe"
        else:
            return "moderate"
    elif weather_id == 800:  # Clear
        return "none"
    elif 801 <= weather_id <= 804:  # Clouds
        return "light"
    else:
        return "unknown"

def get_travel_recommendation(weather_data: dict) -> dict:
    """Generate travel recommendations based on weather data"""
    main = weather_data.get("main", {})
    weather = weather_data.get("weather", [{}])[0]
    wind = weather_data.get("wind", {})
    
    temp = main.get("temp", 0)
    humidity = main.get("humidity", 0)
    condition_id = weather.get("id", 800)
    wind_speed = wind.get("speed", 0)
    
    # Calculate recommendation score (0-10)
    score = 10
    warnings = []
    activities = []
    clothing = []
    tips = []
    
    # Temperature scoring
    if 18 <= temp <= 25:  # Ideal temperature range
        score += 0
        activities.extend(["Walking", "Sightseeing", "Outdoor dining"])
        clothing.append("Light comfortable clothing")
    elif 10 <= temp < 18:  # Cool
        score -= 1
        activities.extend(["Museums", "Indoor attractions", "Cafes"])
        clothing.extend(["Light jacket", "Long pants"])
    elif 25 < temp <= 30:  # Warm
        score -= 1
        activities.extend(["Swimming", "Beach activities", "Early morning walks"])
        clothing.extend(["Light clothing", "Sun hat", "Sunscreen"])
    elif temp < 10:  # Cold
        score -= 2
        warnings.append("Cold weather - dress warmly")
        activities.extend(["Indoor attractions", "Museums", "Shopping"])
        clothing.extend(["Warm jacket", "Gloves", "Warm clothing"])
    elif temp > 30:  # Hot
        score -= 2
        warnings.append("Very hot weather - stay hydrated")
        activities.extend(["Indoor attractions", "Swimming", "Evening activities"])
        clothing.extend(["Very light clothing", "Sun protection", "Hat"])
    
    # Weather condition scoring
    severity = get_weather_severity_level(condition_id)
    if severity == "severe":
        score -= 4
        warnings.append("Severe weather conditions")
        activities = ["Indoor activities only"]
        tips.append("Stay indoors and monitor weather updates")
    elif severity == "moderate":
        score -= 2
        warnings.append("Moderate weather conditions")
        tips.append("Check weather updates regularly")
    elif severity == "light":
        score -= 1
        tips.append("Light weather conditions - plan accordingly")
    
    # Wind scoring
    if wind_speed > 10:  # Strong wind
        score -= 1
        warnings.append("Strong winds")
        tips.append("Secure loose items")
    
    # Humidity scoring
    if humidity > 80:
        score -= 1
        warnings.append("High humidity")
        tips.append("Stay hydrated and take breaks")
    
    # Ensure score is within bounds
    score = max(0, min(10, score))
    
    # Generate recommendation text
    if score >= 8:
        recommendation_text = "Excellent weather for travel and outdoor activities!"
    elif score >= 6:
        recommendation_text = "Good weather conditions with minor considerations."
    elif score >= 4:
        recommendation_text = "Fair weather - some activities may be affected."
    elif score >= 2:
        recommendation_text = "Poor weather conditions - plan indoor activities."
    else:
        recommendation_text = "Severe weather - avoid outdoor activities."
    
    return {
        "score": score,
        "recommendation_text": recommendation_text,
        "warnings": warnings,
        "activities": activities,
        "clothing": clothing,
        "tips": tips
    }

async def get_weather_comparison(locations: List[Dict[str, Any]]) -> dict:
    """Compare weather across multiple locations"""
    weather_data = []
    
    for location in locations:
        try:
            if "lat" in location and "lon" in location:
                weather = await get_current_weather_by_coordinates(
                    location["lat"], location["lon"]
                )
            elif "city" in location:
                weather = await get_current_weather_by_city(
                    location["city"],
                    location.get("state"),
                    location.get("country")
                )
            else:
                continue
            
            weather_data.append({
                "location": location.get("name", weather.get("name", "Unknown")),
                "weather": weather
            })
        except Exception as e:
            logger.warning(f"Failed to get weather for location {location}: {e}")
            continue
    
    if not weather_data:
        return {"error": "No weather data available for comparison"}
    
    # Analyze weather data
    temperatures = []
    humidities = []
    wind_speeds = []
    
    for data in weather_data:
        weather = data["weather"]
        main = weather.get("main", {})
        wind = weather.get("wind", {})
        
        temperatures.append((data["location"], main.get("temp", 0)))
        humidities.append((data["location"], main.get("humidity", 0)))
        wind_speeds.append((data["location"], wind.get("speed", 0)))
    
    # Find extremes
    warmest = max(temperatures, key=lambda x: x[1]) if temperatures else None
    coldest = min(temperatures, key=lambda x: x[1]) if temperatures else None
    most_humid = max(humidities, key=lambda x: x[1]) if humidities else None
    windiest = max(wind_speeds, key=lambda x: x[1]) if wind_speeds else None
    
    return {
        "locations": weather_data,
        "analysis": {
            "warmest_location": warmest[0] if warmest else None,
            "warmest_temperature": warmest[1] if warmest else None,
            "coldest_location": coldest[0] if coldest else None,
            "coldest_temperature": coldest[1] if coldest else None,
            "most_humid_location": most_humid[0] if most_humid else None,
            "highest_humidity": most_humid[1] if most_humid else None,
            "windiest_location": windiest[0] if windiest else None,
            "highest_wind_speed": windiest[1] if windiest else None
        }
    }