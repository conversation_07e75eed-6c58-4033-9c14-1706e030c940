from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date, time
from enum import Enum

# Enums for various flight-related constants
class TravelClass(str, Enum):
    ECONOMY = "ECONOMY"
    PREMIUM_ECONOMY = "PREMIUM_ECONOMY"
    BUSINESS = "BUSINESS"
    FIRST = "FIRST"

class TravelerType(str, Enum):
    ADULT = "ADULT"
    CHILD = "CHILD"
    INFANT = "INFANT"
    SENIOR = "SENIOR"

class CabinClass(str, Enum):
    ECONOMY = "ECONOMY"
    PREMIUM_ECONOMY = "PREMIUM_ECONOMY"
    BUSINESS = "BUSINESS"
    FIRST = "FIRST"

class Coverage(str, Enum):
    MOST_SEGMENTS = "MOST_SEGMENTS"
    AT_LEAST_ONE_SEGMENT = "AT_LEAST_ONE_SEGMENT"
    ALL_SEGMENTS = "ALL_SEGMENTS"

class FeeType(str, Enum):
    SUPPLIER = "SUPPLIER"
    TICKETING = "TICKETING"
    FORM_OF_PAYMENT = "FORM_OF_PAYMENT"

class TimingQualifier(str, Enum):
    STD = "STD"  # Scheduled Time of Departure
    STA = "STA"  # Scheduled Time of Arrival
    ETD = "ETD"  # Estimated Time of Departure
    ETA = "ETA"  # Estimated Time of Arrival
    ATD = "ATD"  # Actual Time of Departure
    ATA = "ATA"  # Actual Time of Arrival

# Base models for common structures
class Meta(BaseModel):
    count: Optional[int] = None
    links: Optional[Dict[str, str]] = None

class Links(BaseModel):
    self: Optional[str] = None
    next: Optional[str] = None
    previous: Optional[str] = None
    last: Optional[str] = None
    first: Optional[str] = None
    up: Optional[str] = None

# Flight Status API Models
class Timing(BaseModel):
    qualifier: TimingQualifier
    value: str  # ISO 8601 datetime string
    delays: Optional[List[Dict[str, Any]]] = None

class FlightPoint(BaseModel):
    iataCode: str
    departure: Optional[Dict[str, List[Timing]]] = None
    arrival: Optional[Dict[str, List[Timing]]] = None
    terminal: Optional[str] = None
    gate: Optional[str] = None

class FlightDesignator(BaseModel):
    carrierCode: str
    flightNumber: int

class AircraftEquipment(BaseModel):
    aircraftType: str

class Segment(BaseModel):
    boardPointIataCode: str
    offPointIataCode: str
    scheduledSegmentDuration: str  # ISO 8601 duration format
    partnership: Optional[Dict[str, Any]] = None

class Leg(BaseModel):
    boardPointIataCode: str
    offPointIataCode: str
    aircraftEquipment: Optional[AircraftEquipment] = None
    scheduledLegDuration: str  # ISO 8601 duration format

class DatedFlight(BaseModel):
    type: str = "DatedFlight"
    scheduledDepartureDate: str  # YYYY-MM-DD format
    flightDesignator: FlightDesignator
    flightPoints: List[FlightPoint]
    segments: List[Segment]
    legs: List[Leg]
    operationalSuffix: Optional[str] = None

class FlightStatusResponse(BaseModel):
    meta: Meta
    data: List[DatedFlight]

# Flight Offers Search API Models
class Fee(BaseModel):
    amount: str
    type: FeeType

class Price(BaseModel):
    currency: str
    total: str
    base: str
    fees: Optional[List[Fee]] = None
    grandTotal: str
    additionalServices: Optional[List[Dict[str, Any]]] = None
    margins: Optional[List[Dict[str, Any]]] = None

class Co2Emission(BaseModel):
    weight: Optional[int] = None
    weightUnit: Optional[str] = None
    cabin: Optional[CabinClass] = None

class Aircraft(BaseModel):
    code: str

class Operating(BaseModel):
    carrierCode: Optional[str] = None

class FlightSegment(BaseModel):
    departure: Dict[str, Any]  # Contains iataCode, terminal, at (datetime)
    arrival: Dict[str, Any]    # Contains iataCode, terminal, at (datetime)
    carrierCode: str
    number: str
    aircraft: Optional[Aircraft] = None
    operating: Optional[Operating] = None
    duration: Optional[str] = None  # ISO 8601 duration
    id: str
    numberOfStops: Optional[int] = None
    blacklistedInEU: Optional[bool] = None
    co2Emissions: Optional[List[Co2Emission]] = None

class Itinerary(BaseModel):
    duration: str  # ISO 8601 duration
    segments: List[FlightSegment]

class TravelerPricing(BaseModel):
    travelerId: str
    fareOption: str
    travelerType: TravelerType
    price: Optional[Price] = None
    fareDetailsBySegment: Optional[List[Dict[str, Any]]] = None

class FlightOffer(BaseModel):
    type: str = "flight-offer"
    id: str
    source: str
    instantTicketingRequired: bool
    nonHomogeneous: bool
    oneWay: bool
    lastTicketingDate: Optional[str] = None
    lastTicketingDateTime: Optional[str] = None
    numberOfBookableSeats: Optional[int] = None
    itineraries: List[Itinerary]
    price: Price
    pricingOptions: Optional[Dict[str, Any]] = None
    validatingAirlineCodes: Optional[List[str]] = None
    travelerPricings: Optional[List[TravelerPricing]] = None

class FlightOffersResponse(BaseModel):
    meta: Optional[Meta] = None
    data: List[FlightOffer]
    dictionaries: Optional[Dict[str, Any]] = None

# Flight Cheapest Date Search API Models
class FlightDate(BaseModel):
    type: str = "flight-date"
    origin: str
    destination: str
    departureDate: Optional[str] = None  # YYYY-MM-DD format
    returnDate: Optional[str] = None     # YYYY-MM-DD format
    price: Price
    links: Optional[Dict[str, str]] = None

class FlightDatesResponse(BaseModel):
    meta: Optional[Meta] = None
    data: List[FlightDate]

# Request models for POST operations
class DepartureDateTimeRange(BaseModel):
    date: str  # YYYY-MM-DD format
    time: Optional[str] = None  # HH:MM:SS format
    dateWindow: Optional[str] = None
    timeWindow: Optional[str] = None

class OriginDestination(BaseModel):
    id: str
    originLocationCode: str
    destinationLocationCode: str
    departureDateTimeRange: DepartureDateTimeRange
    arrivalDateTimeRange: Optional[DepartureDateTimeRange] = None
    alternativeOriginsCodes: Optional[List[str]] = None
    alternativeDestinationsCodes: Optional[List[str]] = None

class Traveler(BaseModel):
    id: str
    travelerType: TravelerType
    associatedAdultId: Optional[str] = None

class CabinRestriction(BaseModel):
    cabin: CabinClass
    coverage: Coverage
    originDestinationIds: List[str]

class CarrierRestrictions(BaseModel):
    blacklistedInEUAllowed: Optional[bool] = None
    excludedCarrierCodes: Optional[List[str]] = None
    includedCarrierCodes: Optional[List[str]] = None

class ConnectionRestriction(BaseModel):
    maxNumberOfConnections: Optional[int] = None
    nonStopPreferred: Optional[bool] = None
    airportChangeAllowed: Optional[bool] = None
    technicalStopsAllowed: Optional[bool] = None

class FlightFilters(BaseModel):
    crossBorderAllowed: Optional[bool] = None
    moreOvernightsAllowed: Optional[bool] = None
    returnToDepartureAirport: Optional[bool] = None
    railSegmentAllowed: Optional[bool] = None
    busSegmentAllowed: Optional[bool] = None
    maxFlightTime: Optional[str] = None
    cabinRestrictions: Optional[List[CabinRestriction]] = None
    carrierRestrictions: Optional[CarrierRestrictions] = None
    connectionRestriction: Optional[ConnectionRestriction] = None

class SearchCriteria(BaseModel):
    excludeAllotments: Optional[bool] = None
    addOneWayOffers: Optional[bool] = None
    maxFlightOffers: Optional[int] = None
    maxPrice: Optional[int] = None
    allowAlternativeFareOptions: Optional[bool] = None
    oneFlightOfferPerDay: Optional[bool] = None
    additionalInformation: Optional[Dict[str, Any]] = None
    pricingOptions: Optional[Dict[str, Any]] = None
    flightFilters: Optional[FlightFilters] = None

class FlightOffersSearchRequest(BaseModel):
    currencyCode: Optional[str] = None
    originDestinations: List[OriginDestination]
    travelers: List[Traveler]
    sources: List[str]
    searchCriteria: Optional[SearchCriteria] = None

# Error models
class AmadeusError(BaseModel):
    status: int
    code: int
    title: str
    detail: Optional[str] = None
    source: Optional[Dict[str, Any]] = None

class AmadeusErrorResponse(BaseModel):
    errors: List[AmadeusError]

# Additional utility models
class Airport(BaseModel):
    iataCode: str
    name: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None

class Airline(BaseModel):
    iataCode: str
    icaoCode: Optional[str] = None
    name: Optional[str] = None

class FlightSearchParams(BaseModel):
    """Simplified model for basic flight search parameters"""
    originLocationCode: str
    destinationLocationCode: str
    departureDate: str  # YYYY-MM-DD format
    returnDate: Optional[str] = None  # YYYY-MM-DD format
    adults: int = 1
    children: Optional[int] = None
    infants: Optional[int] = None
    travelClass: Optional[TravelClass] = None
    includedAirlineCodes: Optional[str] = None  # Comma-separated airline codes
    excludedAirlineCodes: Optional[str] = None  # Comma-separated airline codes
    nonStop: Optional[bool] = None
    currencyCode: Optional[str] = "USD"
    maxPrice: Optional[int] = None
    max: Optional[int] = None  # Maximum number of results

class FlightStatusParams(BaseModel):
    """Parameters for flight status check"""
    carrierCode: str
    flightNumber: str
    scheduledDepartureDate: str  # YYYY-MM-DD format
    operationalSuffix: Optional[str] = None

class FlightDatesParams(BaseModel):
    """Parameters for cheapest flight dates search"""
    origin: str
    destination: Optional[str] = None
    departureDate: Optional[str] = None  # Can be multiple dates comma-separated
    oneWay: Optional[bool] = None
    duration: Optional[int] = None  # Duration in days
    nonStop: Optional[bool] = None
    maxPrice: Optional[float] = None
    viewBy: Optional[str] = None  # DATE, DURATION, WEEK