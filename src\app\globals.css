@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom slider styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, #e5e7eb 0%, #e5e7eb 100%);
  outline: none;
  border-radius: 8px;
  height: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #010066, #FFCC00);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(1, 0, 102, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(1, 0, 102, 0.4);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #010066, #FFCC00);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(1, 0, 102, 0.3);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(1, 0, 102, 0.4);
}

/* Custom scrollbar for the slideover */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #010066, #FFCC00);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #010066, #FF6B35);
}
