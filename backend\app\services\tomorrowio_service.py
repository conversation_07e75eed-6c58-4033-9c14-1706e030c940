# app/services/tomorrowio_service.py
import os
import aiohttp
import asyncio
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
import logging

from app.models.tomorrowio_model import (
    RealtimeWeatherResponse,
    WeatherFields,
    Units,
    TomorrowIOError,
    get_weather_description,
    calculate_comfort_score
)

# Configure logging
logger = logging.getLogger(__name__)

# Tomorrow.io API configuration
TOMORROW_IO_BASE_URL = "https://api.tomorrow.io/v4"
TOMORROW_IO_TIMEOUT = 30

def get_tomorrow_io_credentials() -> str:
    """Get Tomorrow.io API key from environment variables"""
    api_key = os.getenv("TOMORROW_IO_API_KEY")
    if not api_key:
        raise ValueError("TOMORROW_IO_API_KEY environment variable must be set")
    return api_key

async def tomorrow_io_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    method: str = "GET"
) -> Dict[str, Any]:
    """Make a request to Tomorrow.io API"""
    try:
        api_key = get_tomorrow_io_credentials()
        
        # Prepare URL and parameters
        url = f"{TOMORROW_IO_BASE_URL}{endpoint}"
        request_params = params or {}
        request_params["apikey"] = api_key
        
        logger.info(f"Tomorrow.io API request: {method} {url}")
        logger.debug(f"Request parameters: {request_params}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=TOMORROW_IO_TIMEOUT)) as session:
            if method.upper() == "GET":
                async with session.get(url, params=request_params) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Tomorrow.io API success: {response.status}")
                        return data
                    else:
                        logger.error(f"Tomorrow.io API error: {response.status} - {response_text}")
                        if response.status == 401:
                            raise TomorrowIOError("Invalid API key", response.status)
                        elif response.status == 404:
                            raise TomorrowIOError("Location not found", response.status)
                        elif response.status == 429:
                            raise TomorrowIOError("API rate limit exceeded", response.status)
                        else:
                            raise TomorrowIOError(f"API error: {response.status}", response.status)
            else:
                raise TomorrowIOError(f"Unsupported HTTP method: {method}")
                
    except aiohttp.ClientError as e:
        logger.error(f"Tomorrow.io API connection error: {str(e)}")
        raise TomorrowIOError(f"Connection error: {str(e)}")
    except TomorrowIOError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def get_realtime_weather(
    location: str,
    fields: Optional[List[str]] = None,
    units: str = "metric"
) -> Dict[str, Any]:
    """
    Get realtime weather data for a location
    
    Args:
        location: Location string (e.g., "London, UK" or "40.7128,-74.0060")
        fields: List of weather fields to retrieve
        units: Units system (metric/imperial)
    
    Returns:
        Dictionary containing realtime weather data
    """
    if fields is None:
        fields = [
            "temperature", "temperatureApparent", "humidity", "windSpeed", 
            "windDirection", "precipitationIntensity", "precipitationProbability",
            "weatherCode", "cloudCover", "uvIndex", "visibility"
        ]
    
    # Format location for Tomorrow.io API
    formatted_location = format_location_for_api(location)
    
    params = {
        "location": formatted_location,
        "fields": ",".join(fields),
        "units": units
    }
    
    return await tomorrow_io_request("/weather/realtime", params)

# Utility functions
def format_location_for_api(location: str) -> str:
    """Format location string for Tomorrow.io API"""
    # Check if it's already coordinates (lat,lon format)
    if "," in location and len(location.split(",")) == 2:
        parts = location.split(",")
        try:
            lat, lon = float(parts[0].strip()), float(parts[1].strip())
            # Return coordinates in the exact format Tomorrow.io expects
            return f"{lat},{lon}"
        except ValueError:
            # Not coordinates, continue with location name mapping
            pass
    
    # Tomorrow.io location name mappings
    location_mappings = {
        "London, UK": "London",
        "London, United Kingdom": "London",
        "New York, NY": "New York",
        "New York, NY, USA": "New York",
        "New York City": "New York",
        "NYC": "New York",
        "Paris, France": "Paris",
        "Paris, FR": "Paris",
        "Tokyo, Japan": "Tokyo",
        "Tokyo, JP": "Tokyo",
        "Berlin, Germany": "Berlin",
        "Berlin, DE": "Berlin",
        "Sydney, Australia": "Sydney",
        "Sydney, AU": "Sydney",
        "Los Angeles, CA": "Los Angeles",
        "Los Angeles, CA, USA": "Los Angeles",
        "LA": "Los Angeles",
        "Chicago, IL": "Chicago",
        "Chicago, IL, USA": "Chicago",
        "Miami, FL": "Miami",
        "Miami, FL, USA": "Miami",
        "Atlanta, GA": "Atlanta",
        "Atlanta, GA, USA": "Atlanta",
        "Sacramento, CA": "Sacramento",
        "Sacramento, CA, USA": "Sacramento"
    }
    
    # Return mapped location or original if not found
    mapped_location = location_mappings.get(location, location)
    
    # If original location has commas but wasn't mapped, try just the first part
    if "," in mapped_location and mapped_location == location:
        mapped_location = location.split(",")[0].strip()
    
    return mapped_location

def validate_location(location: str) -> bool:
    """Validate location string format"""
    if not location or not isinstance(location, str):
        return False
    
    # Empty string is invalid
    if len(location.strip()) == 0:
        return False
    
    # Check if it's coordinates (lat,lon)
    if "," in location:
        parts = location.split(",")
        if len(parts) == 2:
            try:
                lat, lon = float(parts[0].strip()), float(parts[1].strip())
                return -90 <= lat <= 90 and -180 <= lon <= 180
            except ValueError:
                # If it's not valid coordinates, treat as location name
                # Location names with commas are valid (e.g., "London, UK")
                return True
    
    # Pure numeric strings like "123" are questionable as location names
    # but Tomorrow.io might accept them, so we'll be permissive
    return True

def get_field_validation(fields: List[str]) -> List[str]:
    """Validate and filter weather fields"""
    valid_fields = [field.value for field in WeatherFields]
    return [field for field in fields if field in valid_fields]