{"summary": {"total": 11, "passed": 11, "failed": 0, "success_rate": 100.0}, "results": [{"test": "Realtime Weather - London", "success": true, "timestamp": "2025-08-30T14:23:50.054658", "details": {"location": "City of London, Greater London, England, United Kingdom", "temperature": 13.7, "humidity": 87, "wind_speed": 1.1, "weather_code": 1100}}, {"test": "Realtime Weather - Re<PERSON>d <PERSON>", "success": true, "timestamp": "2025-08-30T14:23:50.055675", "details": {"missing_fields": [], "available_fields": ["altimeterSetting", "cloudBase", "cloudCeiling", "cloudCover", "dewPoint", "freezingRainIntensity", "humidity", "precipitationProbability", "pressureSeaLevel", "pressureSurfaceLevel", "rainIntensity", "sleetIntensity", "snowIntensity", "temperature", "temperatureApparent", "uvHealthConcern", "uvIndex", "visibility", "weatherCode", "windDirection", "windGust", "windSpeed"]}}, {"test": "Realtime Weather - Coordinates", "success": true, "timestamp": "2025-08-30T14:23:50.446325", "details": {"location": "New York (coordinates)", "temperature": 62.9, "units": "imperial"}}, {"test": "Realtime Weather - <PERSON><PERSON><PERSON>", "success": true, "timestamp": "2025-08-30T14:23:50.833177", "details": {"location": "Paris, France", "available_fields": 10, "total_default_fields": 11, "fields": ["temperature", "temperatureApparent", "humidity", "windSpeed", "windDirection", "precipitationProbability", "weatherCode", "cloudCover", "uvIndex", "visibility"]}}, {"test": "Location Validation", "success": true, "timestamp": "2025-08-30T14:23:50.834143", "details": {"test_cases": 6, "passed": 6, "all_tests_passed": true}}, {"test": "Location Formatting", "success": true, "timestamp": "2025-08-30T14:23:50.835085", "details": {"original": "London, UK", "formatted": "London"}}, {"test": "Field Validation", "success": true, "timestamp": "2025-08-30T14:23:50.836096", "details": {"input_fields": ["temperature", "humidity", "<PERSON><PERSON><PERSON>"], "validated_fields": ["temperature", "humidity"], "filtered_count": 2}}, {"test": "Weather Description", "success": true, "timestamp": "2025-08-30T14:23:50.837851", "details": {"weather_code": 1000, "description": "Clear, Sunny"}}, {"test": "Comfort Score Calculation", "success": true, "timestamp": "2025-08-30T14:23:50.838484", "details": {"temperature": 22, "humidity": 60, "wind_speed": 5, "comfort_score": 10.0}}, {"test": "Error <PERSON> - Invalid Location", "success": true, "timestamp": "2025-08-30T14:23:51.325814", "details": {"expected_error": "API error: 400"}}, {"test": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "success": true, "timestamp": "2025-08-30T14:23:51.729045", "details": {"filtered_fields": []}}]}