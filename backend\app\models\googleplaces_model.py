# app/models/googleplaces_model.py
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum

class PlaceType(str, Enum):
    """Google Places API place types"""
    RESTAURANT = "restaurant"
    TOURIST_ATTRACTION = "tourist_attraction"
    LODGING = "lodging"
    POINT_OF_INTEREST = "point_of_interest"
    ESTABLISHMENT = "establishment"
    FOOD = "food"
    STORE = "store"
    SHOPPING_MALL = "shopping_mall"
    MUSEUM = "museum"
    PARK = "park"
    HOSPITAL = "hospital"
    PHARMACY = "pharmacy"
    GAS_STATION = "gas_station"
    ATM = "atm"
    BANK = "bank"
    CHURCH = "church"
    MOSQUE = "mosque"
    SYNAGOGUE = "synagogue"
    HINDU_TEMPLE = "hindu_temple"
    BUDDHIST_TEMPLE = "buddhist_temple"

class BusinessStatus(str, Enum):
    """Business operational status"""
    OPERATIONAL = "OPERATIONAL"
    CLOSED_TEMPORARILY = "CLOSED_TEMPORARILY"
    CLOSED_PERMANENTLY = "CLOSED_PERMANENTLY"

class PriceLevel(int, Enum):
    """Price level from 0 (free) to 4 (very expensive)"""
    FREE = 0
    INEXPENSIVE = 1
    MODERATE = 2
    EXPENSIVE = 3
    VERY_EXPENSIVE = 4

class OpeningHoursPeriod(BaseModel):
    """Opening hours period"""
    open: Dict[str, Union[str, int]]
    close: Optional[Dict[str, Union[str, int]]] = None

class OpeningHours(BaseModel):
    """Opening hours information"""
    open_now: Optional[bool] = None
    periods: Optional[List[OpeningHoursPeriod]] = None
    weekday_text: Optional[List[str]] = None

class PlaceGeometry(BaseModel):
    """Place geometry information"""
    location: Dict[str, float]  # {"lat": float, "lng": float}
    viewport: Optional[Dict[str, Dict[str, float]]] = None

class PlacePhoto(BaseModel):
    """Place photo information"""
    height: int
    width: int
    photo_reference: str
    html_attributions: List[str] = []

class PlaceReview(BaseModel):
    """Place review information"""
    author_name: str
    author_url: Optional[str] = None
    language: Optional[str] = None
    profile_photo_url: Optional[str] = None
    rating: int = Field(..., ge=1, le=5)
    relative_time_description: str
    text: str
    time: int

class AddressComponent(BaseModel):
    """Address component"""
    long_name: str
    short_name: str
    types: List[str]

class PlusCode(BaseModel):
    """Plus code information"""
    compound_code: Optional[str] = None
    global_code: str

class PlaceDetails(BaseModel):
    """Google Places API Place Details response model"""
    place_id: str
    name: Optional[str] = None
    formatted_address: Optional[str] = None
    formatted_phone_number: Optional[str] = None
    international_phone_number: Optional[str] = None
    website: Optional[str] = None
    url: Optional[str] = None
    rating: Optional[float] = Field(None, ge=1.0, le=5.0)
    user_ratings_total: Optional[int] = None
    price_level: Optional[PriceLevel] = None
    business_status: Optional[BusinessStatus] = None
    types: List[str] = []
    
    # Location information
    geometry: Optional[PlaceGeometry] = None
    plus_code: Optional[PlusCode] = None
    address_components: Optional[List[AddressComponent]] = None
    
    # Additional details
    opening_hours: Optional[OpeningHours] = None
    photos: Optional[List[PlacePhoto]] = None
    reviews: Optional[List[PlaceReview]] = None
    
    # Attributes
    wheelchair_accessible_entrance: Optional[bool] = None
    delivery: Optional[bool] = None
    dine_in: Optional[bool] = None
    takeout: Optional[bool] = None
    reservable: Optional[bool] = None
    serves_breakfast: Optional[bool] = None
    serves_lunch: Optional[bool] = None
    serves_dinner: Optional[bool] = None
    serves_beer: Optional[bool] = None
    serves_wine: Optional[bool] = None
    serves_vegetarian_food: Optional[bool] = None
    
    # Additional fields
    vicinity: Optional[str] = None
    icon: Optional[str] = None
    icon_background_color: Optional[str] = None
    icon_mask_base_uri: Optional[str] = None
    reference: Optional[str] = None
    scope: Optional[str] = None
    utc_offset: Optional[int] = None
    adr_address: Optional[str] = None
    editorial_summary: Optional[Dict[str, str]] = None

class PlaceDetailsRequest(BaseModel):
    """Request model for Place Details"""
    place_id: str
    fields: Optional[List[str]] = None
    language: Optional[str] = "en"
    region: Optional[str] = None
    session_token: Optional[str] = None

    @validator('fields')
    def validate_fields(cls, v):
        if v is None:
            return None
        
        valid_fields = {
            # Basic fields
            'place_id', 'name', 'formatted_address', 'geometry', 'types',
            # Contact fields  
            'formatted_phone_number', 'international_phone_number', 'website', 'url',
            # Atmosphere fields
            'rating', 'user_ratings_total', 'price_level', 'business_status',
            # Details fields
            'opening_hours', 'photos', 'reviews', 'address_components', 'plus_code',
            # Service options
            'delivery', 'dine_in', 'takeout', 'reservable',
            # Dining options
            'serves_breakfast', 'serves_lunch', 'serves_dinner', 'serves_beer', 
            'serves_wine', 'serves_vegetarian_food',
            # Accessibility
            'wheelchair_accessible_entrance',
            # Additional
            'vicinity', 'icon', 'icon_background_color', 'icon_mask_base_uri',
            'reference', 'scope', 'utc_offset', 'adr_address', 'editorial_summary'
        }
        
        invalid_fields = set(v) - valid_fields
        if invalid_fields:
            raise ValueError(f"Invalid fields: {invalid_fields}")
        
        return v

class PlacePhotoRequest(BaseModel):
    """Request model for Place Photo"""
    photo_reference: str
    max_width: Optional[int] = Field(None, ge=1, le=1600)
    max_height: Optional[int] = Field(None, ge=1, le=1600)

    @validator('max_width', 'max_height')
    def validate_dimensions(cls, v):
        if v is not None and (v < 1 or v > 1600):
            raise ValueError("Photo dimensions must be between 1 and 1600 pixels")
        return v

class PlaceDetailsResponse(BaseModel):
    """Response model for Place Details API"""
    result: PlaceDetails
    status: str
    html_attributions: List[str] = []
    info_messages: Optional[List[str]] = None

class GooglePlacesError(Exception):
    """Custom exception for Google Places API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, status: Optional[str] = None):
        self.message = message
        self.status_code = status_code
        self.status = status
        super().__init__(self.message)

# Utility functions
def extract_coordinates(place_details: PlaceDetails) -> tuple[float, float]:
    """Extract latitude and longitude from place details"""
    if not place_details.geometry or not place_details.geometry.location:
        raise ValueError("No geometry information available")
    
    location = place_details.geometry.location
    return location["lat"], location["lng"]

def format_address(place_details: PlaceDetails) -> str:
    """Format address from place details"""
    if place_details.formatted_address:
        return place_details.formatted_address
    elif place_details.vicinity:
        return place_details.vicinity
    else:
        return "Address not available"

def get_place_types_display(types: List[str]) -> List[str]:
    """Convert place types to display-friendly format"""
    type_mapping = {
        "tourist_attraction": "Tourist Attraction",
        "point_of_interest": "Point of Interest", 
        "establishment": "Establishment",
        "lodging": "Hotel/Lodging",
        "restaurant": "Restaurant",
        "food": "Food & Dining",
        "shopping_mall": "Shopping Mall",
        "gas_station": "Gas Station",
        "hindu_temple": "Hindu Temple",
        "buddhist_temple": "Buddhist Temple"
    }
    
    return [type_mapping.get(t, t.replace("_", " ").title()) for t in types]

def is_place_open_now(place_details: PlaceDetails) -> Optional[bool]:
    """Check if place is currently open"""
    if not place_details.opening_hours:
        return None
    return place_details.opening_hours.open_now

def get_price_level_display(price_level: Optional[PriceLevel]) -> str:
    """Get display string for price level"""
    if price_level is None:
        return "Price not available"
    
    price_map = {
        PriceLevel.FREE: "Free",
        PriceLevel.INEXPENSIVE: "Inexpensive ($)",
        PriceLevel.MODERATE: "Moderate ($$)",
        PriceLevel.EXPENSIVE: "Expensive ($$$)",
        PriceLevel.VERY_EXPENSIVE: "Very Expensive ($$$$)"
    }
    
    return price_map.get(price_level, "Unknown")

def validate_place_id(place_id: str) -> bool:
    """Validate Google Places place_id format"""
    if not place_id or not isinstance(place_id, str):
        return False
    
    # Google place IDs are typically alphanumeric with some special characters
    # and have a minimum length
    return len(place_id) >= 10 and place_id.replace("-", "").replace("_", "").isalnum()