#!/usr/bin/env python3
"""
Simple Geoapify API Test

This script tests the Geoapify Location API integration with minimal API calls
to verify the integration is working.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Import service functions
from app.services.geoapify_service import (
    search_locations_by_coordinates,
    validate_coordinates,
    validate_place_categories
)
from app.models.geoapify_model import (
    PlaceType,
    extract_coordinates,
    format_address
)

async def test_geoapify_integration():
    """Test Geoapify integration with minimal API calls"""
    print("🗺️ Geoapify Location API Integration Test")
    print("=" * 60)
    
    results = []
    
    # Test 1: Utility Functions (No API calls)
    print("\n🔧 Testing Utility Functions...")
    
    # Test coordinate validation
    coord_tests = [
        ((40.7580, -73.9855), True),   # Valid NYC coordinates
        ((91, 0), False),              # Invalid latitude
        ((0, 181), False),             # Invalid longitude
        ((-90, -180), True),           # Valid boundary coordinates
    ]
    
    coord_results = []
    for (lat, lon), expected in coord_tests:
        result = validate_coordinates(lat, lon)
        coord_results.append(result == expected)
        print(f"   Coordinates ({lat}, {lon}): {result} (expected: {expected})")
    
    utility_success = all(coord_results)
    print(f"✅ Coordinate Validation: {'PASS' if utility_success else 'FAIL'}")
    results.append(("Coordinate Validation", utility_success))
    
    # Test category validation
    test_categories = ["accommodation", "commercial", "invalid_category"]
    validated_categories = validate_place_categories(test_categories)
    category_success = len(validated_categories) == 2  # Should filter out invalid
    print(f"✅ Category Validation: {'PASS' if category_success else 'FAIL'}")
    print(f"   Input: {test_categories}")
    print(f"   Valid: {validated_categories}")
    results.append(("Category Validation", category_success))
    
    # Test 2: Place Types Enum
    print("\n📋 Testing Place Types...")
    
    place_types = [pt.value for pt in PlaceType]
    types_success = len(place_types) > 5
    print(f"✅ Place Types: {'PASS' if types_success else 'FAIL'}")
    print(f"   Available types: {place_types}")
    results.append(("Place Types", types_success))
    
    # Test 3: Single API Call (if possible)
    print("\n🌐 Testing Single API Call...")
    
    try:
        # Try nearby search (Times Square, NYC)
        response = await search_locations_by_coordinates(
            lat=40.7580,
            lon=-73.9855,
            radius=500,
            limit=3,
            categories=["commercial"]
        )
        
        api_success = (
            isinstance(response, dict) and
            "features" in response and
            len(response["features"]) > 0
        )
        
        print(f"✅ API Call: {'PASS' if api_success else 'FAIL'}")
        results.append(("API Call", api_success))
        
        if api_success:
            features = response["features"]
            print(f"   Found {len(features)} locations near Times Square")
            if features:
                first_feature = features[0]
                props = first_feature.get("properties", {})
                print(f"   First result: {props.get('name', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ API Call: FAIL - {str(e)}")
        results.append(("API Call", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests < total_tests:
        print("\n❌ FAILED TESTS:")
        for test_name, success in results:
            if not success:
                print(f"  - {test_name}")
    
    print("\n✅ Integration test completed!")
    
    # Test environment
    api_key = os.getenv("GEOAPIFY_API_KEY")
    if api_key:
        print(f"🔑 API Key configured: {api_key[:10]}...")
    else:
        print("❌ API Key not configured")
    
    return results

async def main():
    """Main test runner"""
    print("🗺️ Geoapify Location API Integration Test Suite")
    print("Documentation: https://apidocs.geoapify.com/docs/places/#api")
    print()
    
    # Check environment
    api_key = os.getenv("GEOAPIFY_API_KEY")
    if not api_key:
        print("❌ Missing GEOAPIFY_API_KEY environment variable")
        return
    
    print("✅ Environment configured")
    print()
    
    # Run tests
    await test_geoapify_integration()

if __name__ == "__main__":
    asyncio.run(main())